// <PERSON>ript to add search tokens to existing measurements for better search performance
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, updateDoc, doc, writeBatch } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

/**
 * Generate search tokens for a document
 * @param {Object} data - Document data
 * @returns {Array} Array of search tokens
 */
const generateSearchTokens = (data) => {
  const tokens = new Set();
  
  // Add name tokens (split by spaces, convert to lowercase)
  if (data.name) {
    const nameTokens = data.name.toLowerCase().split(/\s+/).filter(token => token.length >= 2);
    nameTokens.forEach(token => tokens.add(token));
    
    // Add partial name tokens for better matching
    nameTokens.forEach(token => {
      for (let i = 2; i <= token.length; i++) {
        tokens.add(token.substring(0, i));
      }
    });
  }
  
  // Add phone number (full and partial)
  if (data.phoneNo) {
    tokens.add(data.phoneNo.toLowerCase());
    // Add phone number without spaces/dashes
    const cleanPhone = data.phoneNo.replace(/[\s\-\(\)]/g, '');
    if (cleanPhone.length >= 3) {
      tokens.add(cleanPhone);
      // Add partial phone numbers
      for (let i = 3; i <= cleanPhone.length; i++) {
        tokens.add(cleanPhone.substring(0, i));
      }
    }
  }
  
  // Add bill number (full and partial)
  if (data.billNo) {
    tokens.add(data.billNo.toLowerCase());
    const billNo = data.billNo.toLowerCase();
    for (let i = 2; i <= billNo.length; i++) {
      tokens.add(billNo.substring(0, i));
    }
  }
  
  return Array.from(tokens);
};

/**
 * Add search tokens to all existing measurements
 */
const addSearchTokensToExistingMeasurements = async () => {
  try {
    console.log('🔄 Starting to add search tokens to existing measurements...');
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const snapshot = await getDocs(measurementsRef);
    
    console.log(`📊 Found ${snapshot.size} measurements to process`);
    
    const batch = writeBatch(db);
    let batchCount = 0;
    let processedCount = 0;
    
    for (const docSnapshot of snapshot.docs) {
      try {
        const data = docSnapshot.data();
        
        // Skip if already has search tokens
        if (data.searchTokens && Array.isArray(data.searchTokens) && data.searchTokens.length > 0) {
          console.log(`⏭️ Skipping ${docSnapshot.id} - already has search tokens`);
          continue;
        }
        
        // Generate search tokens
        const searchTokens = generateSearchTokens(data);
        
        // Add to batch
        const docRef = doc(db, MEASUREMENTS_COLLECTION, docSnapshot.id);
        batch.update(docRef, { searchTokens });
        
        batchCount++;
        processedCount++;
        
        console.log(`✅ Prepared ${docSnapshot.id} with ${searchTokens.length} search tokens`);
        
        // Commit batch every 500 operations (Firestore limit)
        if (batchCount >= 500) {
          console.log(`💾 Committing batch of ${batchCount} updates...`);
          await batch.commit();
          console.log(`✅ Batch committed successfully`);
          
          // Create new batch
          const newBatch = writeBatch(db);
          Object.assign(batch, newBatch);
          batchCount = 0;
        }
        
      } catch (docError) {
        console.error(`❌ Error processing document ${docSnapshot.id}:`, docError);
      }
    }
    
    // Commit remaining batch
    if (batchCount > 0) {
      console.log(`💾 Committing final batch of ${batchCount} updates...`);
      await batch.commit();
      console.log(`✅ Final batch committed successfully`);
    }
    
    console.log(`🎉 Successfully added search tokens to ${processedCount} measurements`);
    
  } catch (error) {
    console.error('❌ Error adding search tokens:', error);
  }
};

// Run the script
addSearchTokensToExistingMeasurements()
  .then(() => {
    console.log('🏁 Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
