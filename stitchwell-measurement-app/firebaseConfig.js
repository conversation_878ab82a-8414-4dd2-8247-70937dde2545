import { initializeApp } from 'firebase/app';
import { getFirestore, enableNetwork, disableNetwork } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore with performance optimizations
const db = getFirestore(app);

// Initialize Storage
const storage = getStorage(app);

// Performance optimization functions
export const enableFirestoreNetwork = () => enableNetwork(db);
export const disableFirestoreNetwork = () => disableNetwork(db);

export { app, db, storage };