import React from 'react';
import { Image, View, TouchableOpacity, ImageStyle, ViewStyle } from 'react-native';
import { ZoomIcon } from './TabIcons';

interface AspectRatioImageProps {
  source: { uri: string };
  style?: ViewStyle;
  imageStyle?: ImageStyle;
  onPress?: () => void;
  showZoomIcon?: boolean;
  borderRadius?: number;
  backgroundColor?: string;
  height?: number;
}

const AspectRatioImage: React.FC<AspectRatioImageProps> = ({
  source,
  style,
  imageStyle,
  onPress,
  showZoomIcon = false,
  borderRadius = 8,
  backgroundColor = '#f5f5f5',
  height = 200,
}) => {
  const containerStyle: ViewStyle = {
    width: '100%',
    height,
    borderRadius,
    backgroundColor,
    overflow: 'hidden',
    ...style,
  };

  const finalImageStyle: ImageStyle = {
    width: '100%',
    height: '100%',
    ...imageStyle,
  };

  const content = (
    <View style={containerStyle}>
      <Image
        source={source}
        style={finalImageStyle}
        resizeMode="contain"
      />
      {showZoomIcon && (
        <View
          style={{
            position: 'absolute',
            top: 8,
            right: 8,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: 12,
            padding: 6,
          }}
        >
          <ZoomIcon size={14} color="white" />
        </View>
      )}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

export default AspectRatioImage;
