import React, { useState } from 'react';
import { TouchableOpacity, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { YStack, Text, XStack } from 'tamagui';
import { CalendarIcon } from './TabIcons';
import { colors, borderRadius, spacing } from '../theme/colors';

interface DatePickerInputProps {
  value: string;
  onDateChange: (date: string) => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
}

const DatePickerInput: React.FC<DatePickerInputProps> = ({
  value,
  onDateChange,
  placeholder = 'Select date',
  label,
  required = false,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(() => {
    if (value) {
      // Parse DD-MM-YYYY format
      const [day, month, year] = value.split('-');
      if (day && month && year) {
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
    }
    return new Date();
  });

  const formatDate = (date: Date): string => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  const handleDateChange = (event: any, date?: Date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }
    
    if (date) {
      setSelectedDate(date);
      const formattedDate = formatDate(date);
      onDateChange(formattedDate);
    }
  };

  const openPicker = () => {
    setShowPicker(true);
  };

  const closePicker = () => {
    setShowPicker(false);
  };

  return (
    <YStack space="$2">
      {label && (
        <Text
          fontSize={14}
          fontWeight="600"
          color={colors.text}
          fontFamily="$body"
        >
          {label} {required && '*'}
        </Text>
      )}
      
      <TouchableOpacity
        onPress={openPicker}
        style={{
          backgroundColor: colors.card,
          borderColor: colors.border,
          borderWidth: 1,
          borderRadius: borderRadius.input,
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.sm,
          minHeight: 48,
          justifyContent: 'center',
        }}
        activeOpacity={0.7}
      >
        <XStack alignItems="center" justifyContent="space-between">
          <Text
            fontSize={16}
            color={value ? colors.text : colors.textSecondary}
            fontFamily="$body"
          >
            {value || placeholder}
          </Text>
          <CalendarIcon size={20} color={colors.textSecondary} />
        </XStack>
      </TouchableOpacity>

      {showPicker && (
        <DateTimePicker
          value={selectedDate}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleDateChange}
          maximumDate={new Date(new Date().getFullYear() + 2, 11, 31)}
          style={{
            backgroundColor: colors.card,
          }}
        />
      )}
      
      {Platform.OS === 'ios' && showPicker && (
        <XStack justifyContent="flex-end" space="$2" marginTop="$2">
          <TouchableOpacity
            onPress={closePicker}
            style={{
              backgroundColor: colors.primary,
              paddingHorizontal: spacing.md,
              paddingVertical: spacing.xs,
              borderRadius: borderRadius.button,
            }}
          >
            <Text color="white" fontWeight="600">Done</Text>
          </TouchableOpacity>
        </XStack>
      )}
    </YStack>
  );
};

export default DatePickerInput;
