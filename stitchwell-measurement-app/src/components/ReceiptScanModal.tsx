import React, { useState } from 'react';
import {
  Modal,
  View,
  Alert,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Platform,
  ActionSheetIOS,
} from 'react-native';
import {
  YStack,
  XStack,
  Text,
  Button,
  Card,
  Separator,
} from 'tamagui';
import * as ImagePicker from 'expo-image-picker';
import { colors, borderRadius, spacing } from '../theme/colors';
import { scanReceipt, ExtractedReceiptData, validateExtractedData, getModelDisplayName, getModelStats } from '../services/receiptScanService';
import AspectRatioImage from './AspectRatioImage';
import {
  CameraIcon,
  ImageIcon,
  CheckIcon,
  XIcon,
  AlertTriangleIcon,
  ScanIcon,
} from './TabIcons';

interface ReceiptScanModalProps {
  visible: boolean;
  onClose: () => void;
  onDataExtracted: (data: ExtractedReceiptData) => void;
}

const ReceiptScanModal: React.FC<ReceiptScanModalProps> = ({
  visible,
  onClose,
  onDataExtracted,
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [extractedData, setExtractedData] = useState<ExtractedReceiptData | null>(null);
  const [scanError, setScanError] = useState<string | null>(null);

  const resetModal = () => {
    setSelectedImage(null);
    setExtractedData(null);
    setScanError(null);
    setIsScanning(false);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  const pickImage = async () => {
    const showImagePicker = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
            cancelButtonIndex: 0,
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              openCamera();
            } else if (buttonIndex === 2) {
              openGallery();
            }
          }
        );
      } else {
        Alert.alert(
          'Select Receipt Image',
          'Choose an option',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Take Photo', onPress: openCamera },
            { text: 'Choose from Gallery', onPress: openGallery },
          ]
        );
      }
    };

    const openCamera = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Allow natural aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
        setScanError(null);
        setExtractedData(null);
      }
    };

    const openGallery = async () => {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Allow natural aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
        setScanError(null);
        setExtractedData(null);
      }
    };

    showImagePicker();
  };

  const handleScanReceipt = async () => {
    if (!selectedImage) return;

    setIsScanning(true);
    setScanError(null);
    setExtractedData(null);

    try {
      const result = await scanReceipt(selectedImage);

      if (result.success && result.data) {
        setExtractedData(result.data);

        // Include the image URI in the extracted data
        const dataWithImage = {
          ...result.data,
          imageUri: selectedImage
        };

        // Automatically use the extracted data without requiring user confirmation
        onDataExtracted(dataWithImage);

        // Show success message with confidence and any validation warnings
        const validationIssues = validateExtractedData(result.data);
        const extractedFields = [];
        if (result.data.customerName) extractedFields.push('Customer Name');
        if (result.data.phoneNumber) extractedFields.push('Phone Number');
        if (result.data.billNumber) extractedFields.push('Bill Number');
        if (result.data.amount) extractedFields.push('Amount');
        if (result.data.deliveryDate) extractedFields.push('Delivery Date');

        let message = `Successfully extracted: ${extractedFields.join(', ')}`;
        if (result.modelUsed) {
          const modelDisplayName = getModelDisplayName(result.modelUsed);
          message += `\n\nProcessed by: ${modelDisplayName}`;
        }
        if (validationIssues.length > 0) {
          message += `\n\nPlease verify: ${validationIssues.join(', ')}`;
        }

        Alert.alert(
          'Receipt Scanned Successfully',
          message,
          [{
            text: 'OK',
            onPress: () => handleClose() // Auto-close modal after success
          }]
        );
      } else {
        setScanError(result.error || 'Failed to extract data from receipt');
      }
    } catch (error) {
      setScanError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsScanning(false);
    }
  };



  const renderExtractedDataPreview = () => {
    if (!extractedData) return null;

    return (
      <Card
        backgroundColor={colors.card}
        borderRadius={borderRadius.medium}
        padding={spacing.md}
        borderWidth={1}
        borderColor={colors.border}
        marginTop={spacing.md}
      >
        <XStack alignItems="center" justifyContent="space-between" marginBottom={spacing.sm}>
          <Text fontSize={16} fontWeight="600" color={colors.text}>
            Extracted Data
          </Text>
          <XStack alignItems="center" space="$1">
            <CheckIcon size={16} color={colors.success} />
            <Text fontSize={12} color={colors.success}>
              {extractedData.confidence}% confidence
            </Text>
          </XStack>
        </XStack>

        <YStack space="$2">
          {extractedData.customerName && (
            <XStack justifyContent="space-between">
              <Text fontSize={14} color={colors.textSecondary}>Customer:</Text>
              <Text fontSize={14} color={colors.text} fontWeight="500">
                {extractedData.customerName}
              </Text>
            </XStack>
          )}
          
          {extractedData.phoneNumber && (
            <XStack justifyContent="space-between">
              <Text fontSize={14} color={colors.textSecondary}>Phone:</Text>
              <Text fontSize={14} color={colors.text} fontWeight="500">
                {extractedData.phoneNumber}
              </Text>
            </XStack>
          )}
          
          {extractedData.billNumber && (
            <XStack justifyContent="space-between">
              <Text fontSize={14} color={colors.textSecondary}>Bill #:</Text>
              <Text fontSize={14} color={colors.text} fontWeight="500">
                {extractedData.billNumber}
              </Text>
            </XStack>
          )}
          
          {extractedData.amount && (
            <XStack justifyContent="space-between">
              <Text fontSize={14} color={colors.textSecondary}>Amount:</Text>
              <Text fontSize={14} color={colors.text} fontWeight="500">
                ₹{extractedData.amount}
              </Text>
            </XStack>
          )}
          
          {extractedData.deliveryDate && (
            <XStack justifyContent="space-between">
              <Text fontSize={14} color={colors.textSecondary}>Delivery:</Text>
              <Text fontSize={14} color={colors.text} fontWeight="500">
                {extractedData.deliveryDate}
              </Text>
            </XStack>
          )}
        </YStack>

        {extractedData.extractedText && (
          <>
            <Separator marginVertical={spacing.sm} />
            <Text fontSize={12} color={colors.textTertiary}>
              Detected text: {extractedData.extractedText}
            </Text>
          </>
        )}
      </Card>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <YStack f={1} backgroundColor={colors.background}>
        {/* Header */}
        <YStack
          backgroundColor={colors.card}
          paddingHorizontal={spacing.lg}
          paddingTop={spacing.xl}
          paddingBottom={spacing.md}
          borderBottomWidth={1}
          borderBottomColor={colors.border}
        >
          <XStack alignItems="center" justifyContent="space-between">
            <YStack>
              <Text fontSize={20} fontWeight="700" color={colors.text}>
                Scan Bill
              </Text>
              <Text fontSize={14} color={colors.textSecondary}>
                Auto-fill form with extracted data
              </Text>
              <Text fontSize={12} color={colors.textTertiary} marginTop="$1">
                {(() => {
                  const stats = getModelStats();
                  return `${stats.total} Google AI vision models available`;
                })()}
              </Text>
            </YStack>
            <TouchableOpacity
              onPress={handleClose}
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: colors.backgroundAlt,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <XIcon size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          </XStack>
        </YStack>

        {/* Content */}
        <YStack f={1} padding={spacing.lg}>
          {/* Image Selection */}
          {!selectedImage ? (
            <TouchableOpacity
              onPress={pickImage}
              style={{
                height: 200,
                borderRadius: borderRadius.medium,
                borderWidth: 2,
                borderColor: colors.border,
                borderStyle: 'dashed',
                backgroundColor: colors.backgroundAlt,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <YStack alignItems="center" space="$3">
                <ScanIcon size={48} color={colors.textTertiary} />
                <Text fontSize={16} color={colors.text} fontWeight="500">
                  Select Receipt Image
                </Text>
                <Text fontSize={14} color={colors.textSecondary} textAlign="center">
                  Take a photo or choose from gallery{'\n'}Data will be automatically filled in the form
                </Text>
              </YStack>
            </TouchableOpacity>
          ) : (
            <YStack space="$3">
              <AspectRatioImage
                source={{ uri: selectedImage }}
                borderRadius={borderRadius.medium}
                backgroundColor={colors.backgroundAlt}
                height={200}
              />
              
              <XStack space="$2">
                <Button
                  flex={1}
                  onPress={pickImage}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderWidth={1}
                >
                  <XStack alignItems="center" space="$2">
                    <CameraIcon size={16} color={colors.textSecondary} />
                    <Text color={colors.textSecondary}>Change</Text>
                  </XStack>
                </Button>
                
                <Button
                  flex={2}
                  onPress={handleScanReceipt}
                  disabled={isScanning}
                  backgroundColor={colors.primary}
                  opacity={isScanning ? 0.6 : 1}
                >
                  <XStack alignItems="center" space="$2">
                    {isScanning ? (
                      <ActivityIndicator size="small" color="white" />
                    ) : (
                      <ScanIcon size={16} color="white" />
                    )}
                    <Text color="white" fontWeight="500">
                      {isScanning ? 'Scanning...' : 'Scan Bill'}
                    </Text>
                  </XStack>
                </Button>
              </XStack>
            </YStack>
          )}

          {/* Error Display */}
          {scanError && (
            <Card
              backgroundColor={colors.error + '10'}
              borderColor={colors.error}
              borderWidth={1}
              borderRadius={borderRadius.medium}
              padding={spacing.md}
              marginTop={spacing.md}
            >
              <XStack alignItems="center" space="$2">
                <AlertTriangleIcon size={16} color={colors.error} />
                <Text fontSize={14} color={colors.error} flex={1}>
                  {scanError}
                </Text>
              </XStack>
            </Card>
          )}

          {/* Extracted Data Preview */}
          {renderExtractedDataPreview()}

          {/* Scan Again Button - only show if there was an error */}
          {scanError && selectedImage && (
            <YStack marginTop={spacing.lg}>
              <Button
                onPress={() => {
                  setScanError(null);
                  setExtractedData(null);
                }}
                backgroundColor={colors.primary}
                borderRadius={borderRadius.button}
                paddingVertical={spacing.md}
              >
                <XStack alignItems="center" space="$2">
                  <ScanIcon size={16} color="white" />
                  <Text color="white" fontSize={16} fontWeight="600">
                    Try Scanning Again
                  </Text>
                </XStack>
              </Button>
            </YStack>
          )}
        </YStack>
      </YStack>
    </Modal>
  );
};

export default ReceiptScanModal;
