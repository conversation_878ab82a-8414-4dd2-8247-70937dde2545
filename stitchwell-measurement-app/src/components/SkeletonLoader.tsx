import React, { useEffect, useRef } from 'react';
import { Animated, View } from 'react-native';
import { YStack, XStack, Card } from 'tamagui';
import { colors, borderRadius, spacing, shadows } from '../theme/colors';

interface SkeletonLoaderProps {
  count?: number;
}

const SkeletonItem = () => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Card
      marginVertical="$2"
      style={{
        backgroundColor: colors.card,
        borderRadius: borderRadius.card,
        overflow: 'hidden',
        ...shadows.medium,
      }}
    >
      {/* Header gradient skeleton */}
      <Animated.View
        style={{
          height: 40,
          backgroundColor: colors.primary,
          opacity,
          paddingVertical: spacing.xs,
          paddingHorizontal: spacing.sm,
        }}
      >
        <XStack justifyContent="space-between" alignItems="center">
          <Animated.View
            style={{
              width: 120,
              height: 16,
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
              borderRadius: 8,
              opacity,
            }}
          />
          <Animated.View
            style={{
              width: 28,
              height: 28,
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
              borderRadius: 14,
              opacity,
            }}
          />
        </XStack>
      </Animated.View>

      {/* Content skeleton */}
      <YStack padding="$3" gap="$2">
        {/* Customer info */}
        <XStack gap="$2" alignItems="center">
          <Animated.View
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: `${colors.primary}15`,
              opacity,
            }}
          />
          <YStack flex={1} gap="$1">
            <Animated.View
              style={{
                width: '60%',
                height: 16,
                backgroundColor: colors.backgroundAlt,
                borderRadius: 8,
                opacity,
              }}
            />
            <Animated.View
              style={{
                width: '40%',
                height: 12,
                backgroundColor: colors.backgroundAlt,
                borderRadius: 6,
                opacity,
              }}
            />
          </YStack>
          <Animated.View
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: `${colors.primary}15`,
              opacity,
            }}
          />
        </XStack>

        {/* Date info */}
        <XStack gap="$2" alignItems="center">
          <Animated.View
            style={{
              width: 28,
              height: 28,
              borderRadius: 14,
              backgroundColor: `${colors.primary}15`,
              opacity,
            }}
          />
          <Animated.View
            style={{
              width: '50%',
              height: 12,
              backgroundColor: colors.backgroundAlt,
              borderRadius: 6,
              opacity,
            }}
          />
        </XStack>

        {/* Measurement tags */}
        <XStack flexWrap="wrap" gap="$1">
          {[1, 2, 3].map((index) => (
            <Animated.View
              key={index}
              style={{
                width: 60 + (index * 10),
                height: 20,
                backgroundColor: `${colors.primary}10`,
                borderRadius: borderRadius.tag - 2,
                opacity,
              }}
            />
          ))}
        </XStack>
      </YStack>
    </Card>
  );
};

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ count = 5 }) => {
  return (
    <YStack paddingHorizontal={spacing.lg} paddingTop={spacing.md}>
      {Array.from({ length: count }, (_, index) => (
        <SkeletonItem key={index} />
      ))}
    </YStack>
  );
};

export default SkeletonLoader;
