// App-wide constants to avoid hardcoded values

// Date and time constants
export const DATE_CONSTANTS = {
  RECENT_DAYS_THRESHOLD: 7, // Days to consider as "recent"
  MILLISECONDS_PER_DAY: 1000 * 60 * 60 * 24,
};

// UI constants
export const UI_CONSTANTS = {
  // Icon sizes
  ICON_SIZE_SMALL: 16,
  ICON_SIZE_MEDIUM: 18,
  ICON_SIZE_LARGE: 20,
  ICON_SIZE_EXTRA_LARGE: 36,
  
  // Container sizes
  AVATAR_SIZE_SMALL: 28,
  AVATAR_SIZE_MEDIUM: 36,
  AVATAR_SIZE_LARGE: 44,
  AVATAR_SIZE_EXTRA_LARGE: 120,
  
  // Border radius values
  BORDER_RADIUS_SMALL: 20,
  BORDER_RADIUS_MEDIUM: 30,
  BORDER_RADIUS_LARGE: 40,
  BORDER_RADIUS_EXTRA_LARGE: 60,
  
  // Font sizes
  FONT_SIZE_SMALL: 10,
  FONT_SIZE_MEDIUM: 12,
  FONT_SIZE_LARGE: 14,
  FONT_SIZE_EXTRA_LARGE: 16,
  FONT_SIZE_TITLE: 18,
  FONT_SIZE_HEADING: 20,
  FONT_SIZE_LARGE_HEADING: 24,
  FONT_SIZE_HERO: 32,
  
  // Opacity values
  OPACITY_DISABLED: 0.5,
  OPACITY_SECONDARY: 0.7,
  OPACITY_LIGHT: 0.85,
  
  // Letter spacing
  LETTER_SPACING_TIGHT: -1.2,
  LETTER_SPACING_NORMAL: -0.5,
  LETTER_SPACING_WIDE: 0.5,
  
  // Line heights
  LINE_HEIGHT_TIGHT: 20,
  LINE_HEIGHT_NORMAL: 22,
  LINE_HEIGHT_RELAXED: 24,
  LINE_HEIGHT_LOOSE: 36,
};

// Text constants
export const TEXT_CONSTANTS = {
  // Default values
  DEFAULT_CUSTOMER_NAME: 'Unknown Customer',
  
  // Labels and titles
  WELCOME_MESSAGE: 'Welcome Back',
  APP_TITLE: 'Measurements',
  APP_SUBTITLE: 'Manage your tailoring business with precision',
  
  // Status labels
  TOTAL_LABEL: 'Total',
  RECENT_LABEL: 'Recent',
  PENDING_LABEL: 'Pending',
  
  // Empty state messages
  NO_RESULTS_TITLE: 'No results found',
  READY_TO_START_TITLE: 'Ready to start measuring?',
  NO_RESULTS_DESCRIPTION: 'We couldn\'t find any measurements matching your search. Try adjusting your search terms.',
  READY_TO_START_DESCRIPTION: 'Create your first measurement to get started with managing your tailoring business.',
  
  // Button labels
  CLEAR_SEARCH_BUTTON: 'Clear Search',
  ADD_FIRST_MEASUREMENT_BUTTON: 'Add First Measurement',
  LEARN_MORE_BUTTON: 'Learn More',
  TRY_AGAIN_BUTTON: 'Try Again',
  
  // Accessibility labels
  TAP_TO_CALL_HINT: 'Tap to call',
  
  // Prefixes
  BILL_PREFIX: 'Bill #',
  MEASUREMENT_PREFIX: 'Measurement #',
  
  // Emojis
  EMOJI_CHART: '📊',
  EMOJI_LIGHTNING: '⚡',
  EMOJI_CLOCK: '⏰',
  EMOJI_SEARCH: '🔍',
  EMOJI_RULER: '📏',
  EMOJI_WARNING: '⚠️',
  EMOJI_CHECK: '✓',
  EMOJI_SMS: '💬',

  // SMS related
  SMS_TAB_TITLE: 'SMS',
  SMS_SCREEN_TITLE: 'Send SMS',
  SMS_SELECT_CUSTOMERS: 'Select Customers',
  SMS_MESSAGE_PLACEHOLDER: 'Type your message here...',
  SMS_SEND_BUTTON: 'Send SMS',
  SMS_TEMPLATE_DELIVERY: 'Hi {name}, your order #{billNo} is ready for delivery on {deliveryDate}. Please contact us to arrange pickup.',
  SMS_TEMPLATE_PAYMENT: 'Hi {name}, this is a friendly reminder about payment for order #{billNo}. Please contact us for details.',
  SMS_TEMPLATE_FOLLOWUP: 'Hi {name}, thank you for choosing our services! We hope you are satisfied with your order #{billNo}.',
  SMS_NO_CUSTOMERS_SELECTED: 'Please select at least one customer',
  SMS_NO_MESSAGE: 'Please enter a message',
  SMS_SUCCESS: 'SMS sent successfully',
  SMS_ERROR: 'Failed to send SMS',
};

// Business logic constants
export const BUSINESS_CONSTANTS = {
  // ID display length
  ID_DISPLAY_LENGTH: 6,

  // Database limits
  MAX_BILLS_LIMIT: 10000, // Maximum number of bills in database

  // Search debounce delays
  SEARCH_DEBOUNCE_CACHED: 150, // ms for likely cached results
  SEARCH_DEBOUNCE_NEW: 400, // ms for new searches
  SEARCH_MIN_LENGTH_FOR_CACHE: 3,

  // Refresh timeout
  REFRESH_TIMEOUT: 1000, // ms

  // Connection cache duration
  CONNECTION_CACHE_DURATION: 30000, // 30 seconds
};

// Delivery status types
export const DELIVERY_STATUS = {
  NONE: 'none',
  OVERDUE: 'overdue',
  CURRENT: 'current',
  FUTURE: 'future',
} as const;

// Helper function to get current date for dynamic date comparisons
export const getCurrentDate = () => new Date();

// Helper function to get current month start and end
export const getCurrentMonthRange = () => {
  const now = getCurrentDate();
  const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
  return { currentMonth, currentMonthEnd };
};

// Helper function to check if a date is recent
export const isRecentDate = (date: Date, thresholdDays: number = DATE_CONSTANTS.RECENT_DAYS_THRESHOLD): boolean => {
  const today = getCurrentDate();
  const diffTime = Math.abs(today.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / DATE_CONSTANTS.MILLISECONDS_PER_DAY);
  return diffDays <= thresholdDays;
};

// Helper function to check if a delivery date is pending
export const isPendingDelivery = (deliveryDateString: string): boolean => {
  if (!deliveryDateString) return false;
  const today = getCurrentDate();
  const deliveryDate = new Date(deliveryDateString);
  return deliveryDate >= today;
};
