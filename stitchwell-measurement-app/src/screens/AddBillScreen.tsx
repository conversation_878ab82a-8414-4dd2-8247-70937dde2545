import React, { useState } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H2,
  XStack,
  YStack,
  Input,
  Text,
  Separator,
  Form,
  TextArea,
  Card,
} from 'tamagui';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  View,
  Alert,
  TouchableOpacity,
  Image,
  ActionSheetIOS,
  ActivityIndicator
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Measurement } from '../types';
import { colors, borderRadius, spacing, shadows } from '../theme/colors';
import { addMeasurement } from '../services/measurementService';
import { uploadImage } from '../services/storageService';
import { scanReceipt, validateExtractedData, getModelDisplayName } from '../services/receiptScanService';
import {
  UserIcon,
  PhoneIcon,
  BillIcon,
  CalendarIcon,
  MoneyIcon,
  FileTextIcon,
  SaveIcon,
  CameraIcon,
  ImageIcon,
  ZoomIcon,
  ScanIcon
} from '../components/TabIcons';
import ImageZoomModal from '../components/ImageZoomModal';
import DatePickerInput from '../components/DatePickerInput';
import AspectRatioImage from '../components/AspectRatioImage';
import GradientWrapper from '../components/GradientWrapper';
import { ExtractedReceiptData } from '../services/receiptScanService';

type Props = NativeStackScreenProps<RootStackParamList, 'AddBill'>;

const AddBillScreen = ({ navigation, route }: Props) => {
  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [billNumber, setBillNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [billDate, setBillDate] = useState(''); // Date when bill was created
  const [deliveryDate, setDeliveryDate] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [imageZoomVisible, setImageZoomVisible] = useState(false);

  // Request camera permissions
  React.useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Sorry, we need camera roll permissions to upload images.');
      }
    })();
  }, []);

  const pickImage = async () => {
    const showImagePicker = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
            cancelButtonIndex: 0,
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              openCamera();
            } else if (buttonIndex === 2) {
              openGallery();
            }
          }
        );
      } else {
        Alert.alert(
          'Select Image',
          'Choose an option',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Take Photo', onPress: openCamera },
            { text: 'Choose from Gallery', onPress: openGallery },
          ]
        );
      }
    };

    const openCamera = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Allow natural aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    const openGallery = async () => {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Allow natural aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    showImagePicker();
  };

  const handleReceiptScanData = (data: ExtractedReceiptData) => {
    // Auto-populate form fields with extracted data (only if data exists)
    if (data.customerName?.trim()) {
      setCustomerName(data.customerName.trim());
    }
    if (data.phoneNumber?.trim()) {
      setPhoneNumber(data.phoneNumber.trim());
    }
    if (data.billNumber?.trim()) {
      setBillNumber(data.billNumber.trim());
    }
    if (data.amount?.trim()) {
      setAmount(data.amount.trim());
    }
    if (data.billDate?.trim()) {
      setBillDate(data.billDate.trim());
    }
    if (data.deliveryDate?.trim()) {
      setDeliveryDate(data.deliveryDate.trim());
    }

    // Use the scanned image directly if provided - eliminates need to capture twice
    if (data.imageUri) {
      setImageUri(data.imageUri);
    }

    // Count successfully extracted fields
    const extractedFields = [];
    if (data.customerName?.trim()) extractedFields.push('Customer Name');
    if (data.phoneNumber?.trim()) extractedFields.push('Phone Number');
    if (data.billNumber?.trim()) extractedFields.push('Bill Number');
    if (data.amount?.trim()) extractedFields.push('Amount');
    if (data.billDate?.trim()) extractedFields.push('Bill Date');
    if (data.deliveryDate?.trim()) extractedFields.push('Delivery Date');

    // Show informative message about what was extracted
    if (extractedFields.length > 0) {
      console.log(`✅ Successfully populated: ${extractedFields.join(', ')}`);
      console.log(`📊 Extraction confidence: ${data.confidence}%`);
      if (data.imageUri) {
        console.log(`📷 Receipt image automatically added`);
      }
    } else {
      console.log('⚠️ No data could be extracted from the receipt');
    }
  };

  // Direct AI scan function - bypasses modal and goes straight to scanning
  const handleDirectAIScan = async () => {
    const showImagePicker = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
            cancelButtonIndex: 0,
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              openCameraForScan();
            } else if (buttonIndex === 2) {
              openGalleryForScan();
            }
          }
        );
      } else {
        Alert.alert(
          'AI Receipt Scan',
          'Choose an option to scan your receipt',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Take Photo', onPress: openCameraForScan },
            { text: 'Choose from Gallery', onPress: openGalleryForScan },
          ]
        );
      }
    };

    const openCameraForScan = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await performAIScan(result.assets[0].uri);
      }
    };

    const openGalleryForScan = async () => {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await performAIScan(result.assets[0].uri);
      }
    };

    showImagePicker();
  };

  // Perform AI scanning on the selected image
  const performAIScan = async (imageUri: string) => {
    setUploadingImage(true); // Show loading state

    try {
      const result = await scanReceipt(imageUri);

      if (result.success && result.data) {
        // Include the image URI in the extracted data
        const dataWithImage = {
          ...result.data,
          imageUri: imageUri
        };

        // Auto-populate the form
        handleReceiptScanData(dataWithImage);

        // Show success message
        const validationIssues = validateExtractedData(result.data);
        const extractedFields = [];
        if (result.data.customerName) extractedFields.push('Customer Name');
        if (result.data.phoneNumber) extractedFields.push('Phone Number');
        if (result.data.billNumber) extractedFields.push('Bill Number');
        if (result.data.amount) extractedFields.push('Amount');
        if (result.data.billDate) extractedFields.push('Bill Date');
        if (result.data.deliveryDate) extractedFields.push('Delivery Date');

        let message = `Successfully extracted: ${extractedFields.join(', ')}`;
        if (result.modelUsed) {
          const modelDisplayName = getModelDisplayName(result.modelUsed);
          message += `\n\nProcessed by: ${modelDisplayName}`;
        }
        if (validationIssues.length > 0) {
          message += `\n\nPlease verify: ${validationIssues.join(', ')}`;
        }

        Alert.alert('Receipt Scanned Successfully', message);
      } else {
        Alert.alert('Scan Failed', result.error || 'Failed to extract data from receipt');
      }
    } catch (error) {
      Alert.alert('Scan Error', error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setUploadingImage(false);
    }
  };

  const handleRemoveImage = () => {
    Alert.alert(
      'Remove Image',
      'Are you sure you want to remove the selected image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => setImageUri(null)
        },
      ]
    );
  };

  const validateForm = () => {
    // Customer name is no longer required - allow bills without customer names
    if (!phoneNumber.trim()) {
      Alert.alert('Validation Error', 'Phone number is required');
      return false;
    }
    if (!billNumber.trim()) {
      Alert.alert('Validation Error', 'Bill number is required');
      return false;
    }
    if (!amount.trim() || isNaN(parseFloat(amount))) {
      Alert.alert('Validation Error', 'Please enter a valid amount');
      return false;
    }
    if (!deliveryDate.trim()) {
      Alert.alert('Validation Error', 'Delivery date is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      let uploadedImageUrl = '';

      // Upload image if selected
      if (imageUri) {
        setUploadingImage(true);
        try {
          uploadedImageUrl = await uploadImage(imageUri);
        } catch (imageError) {
          console.error('Error uploading image:', imageError);
          Alert.alert('Warning', 'Failed to upload image, but bill will be created without it.');
        } finally {
          setUploadingImage(false);
        }
      }

      // Create a new measurement record with bill information
      // Use scanned bill date if available, otherwise use current date
      let formattedDate: string;
      if (billDate.trim()) {
        formattedDate = billDate.trim();
      } else {
        // Format current date as DD-MM-YYYY to match the expected format in HomeScreen
        const currentDate = new Date();
        formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}-${currentDate.getFullYear()}`;
      }

      const newBill: Partial<Measurement> = {
        name: customerName.trim() || 'Unknown Customer', // Provide default if customer name is empty
        phoneNo: phoneNumber.trim(),
        billNo: billNumber,
        date: formattedDate,
        deliveryDate: deliveryDate.trim(),
        imageUrl: uploadedImageUrl,
        measurements: {}, // Empty measurements object for now
        notes: notes.trim(),
      };

      await addMeasurement(newBill);

      Alert.alert(
        'Success',
        'Bill created successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error creating bill:', error);
      Alert.alert('Error', 'Failed to create bill. Please try again.');
    } finally {
      setLoading(false);
      setUploadingImage(false);
    }
  };

  return (
    <YStack f={1} backgroundColor={colors.background}>
      {/* Modern Gradient Header */}
      <GradientWrapper
        colors={colors.primaryGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          paddingHorizontal: spacing.lg,
          paddingTop: spacing.xl + 10,
          paddingBottom: spacing.lg,
          borderBottomLeftRadius: borderRadius.extraLarge,
          borderBottomRightRadius: borderRadius.extraLarge,
          ...shadows.large,
        }}
      >
        <XStack alignItems="center" justifyContent="space-between" marginBottom="$2">
          <YStack flex={1}>
            <H2
              color="white"
              fontSize={22}
              fontWeight="700"
              fontFamily="$heading"
              letterSpacing={-0.5}
              marginBottom="$1"
            >
              Add New Bill
            </H2>
            <Text
              color="rgba(255,255,255,0.9)"
              fontSize={13}
              fontFamily="$body"
              fontWeight="500"
            >
              Create a billing record for your customer
            </Text>
          </YStack>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: 'rgba(255,255,255,0.2)',
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.3)',
            }}
          >
            <Text fontSize={16} color="white" fontWeight="600">✕</Text>
          </TouchableOpacity>
        </XStack>
      </GradientWrapper>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: spacing.xl }}
        >
          {/* Quick Actions Section */}
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.large}
            marginHorizontal={spacing.lg}
            marginTop={-spacing.md}
            padding={spacing.lg}
            borderWidth={1}
            borderColor={colors.border}
            style={shadows.medium}
          >
            <XStack space="$3" alignItems="center">
              {/* Receipt Scanner */}
              <Button
                flex={1}
                onPress={handleDirectAIScan}
                backgroundColor={colors.accent}
                borderRadius={borderRadius.medium}
                paddingVertical={spacing.sm}
                height={44}
                disabled={uploadingImage}
                opacity={uploadingImage ? 0.6 : 1}
                pressStyle={{
                  backgroundColor: colors.accentDark,
                  scale: 0.98,
                }}
              >
                <XStack alignItems="center" space="$2">
                  {uploadingImage ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <ScanIcon size={16} color="white" />
                  )}
                  <Text color="white" fontSize={13} fontWeight="600">
                    {uploadingImage ? 'Scanning...' : 'AI Scan'}
                  </Text>
                </XStack>
              </Button>

              {/* Image Upload */}
              <Button
                flex={1}
                onPress={pickImage}
                backgroundColor={colors.backgroundAlt}
                borderColor={colors.border}
                borderWidth={1}
                borderRadius={borderRadius.medium}
                paddingVertical={spacing.sm}
                height={44}
                pressStyle={{
                  backgroundColor: colors.border,
                  scale: 0.98,
                }}
              >
                <XStack alignItems="center" space="$2">
                  <CameraIcon size={16} color={colors.textSecondary} />
                  <Text color={colors.textSecondary} fontSize={13} fontWeight="600">
                    Add Photo
                  </Text>
                </XStack>
              </Button>
            </XStack>
          </Card>

          {/* Image Preview Section */}
          {imageUri && (
            <Card
              backgroundColor={colors.card}
              borderRadius={borderRadius.large}
              padding={spacing.md}
              marginHorizontal={spacing.lg}
              marginTop={spacing.md}
              borderWidth={1}
              borderColor={colors.border}
              style={shadows.small}
            >

              <AspectRatioImage
                source={{ uri: imageUri }}
                onPress={() => setImageZoomVisible(true)}
                showZoomIcon={true}
                borderRadius={borderRadius.medium}
                backgroundColor={colors.backgroundAlt}
                height={160}
              />
              <XStack space="$2" marginTop="$3">
                <Button
                  flex={1}
                  onPress={pickImage}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderWidth={1}
                  borderRadius={borderRadius.small}
                  paddingVertical={spacing.xs}
                  height={36}
                >
                  <XStack alignItems="center" space="$1">
                    <CameraIcon size={14} color={colors.textSecondary} />
                    <Text color={colors.textSecondary} fontSize={12} fontWeight="500">Change</Text>
                  </XStack>
                </Button>
                <Button
                  flex={1}
                  onPress={handleRemoveImage}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderWidth={1}
                  borderRadius={borderRadius.small}
                  paddingVertical={spacing.xs}
                  height={36}
                >
                  <Text color={colors.textSecondary} fontSize={12} fontWeight="500">Remove</Text>
                </Button>
              </XStack>
            </Card>
          )}

          {/* Customer Information */}
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.large}
            padding={spacing.lg}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
            style={shadows.small}
          >
            <Text
              fontSize={16}
              fontWeight="700"
              color={colors.text}
              fontFamily="$heading"
              marginBottom="$4"
              letterSpacing={-0.3}
            >
              Customer Details
            </Text>

            <YStack space="$3">
              {/* Customer Name */}
              <YStack space="$2">
                <Text
                  fontSize={13}
                  fontWeight="600"
                  color={colors.text}
                  fontFamily="$body"
                >
                  Customer Name
                </Text>
                <Input
                  placeholder="Enter customer name"
                  value={customerName}
                  onChangeText={setCustomerName}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderRadius={borderRadius.input}
                  paddingHorizontal={spacing.md}
                  paddingVertical={spacing.sm}
                  fontSize={15}
                  height={44}
                  borderBottomWidth={0}
                  style={{
                    borderBottomWidth: 0,
                    borderBottomColor: 'transparent',
                  }}
                  focusStyle={{
                    borderColor: colors.primary,
                    backgroundColor: colors.card,
                  }}
                />
              </YStack>

              {/* Phone Number */}
              <YStack space="$2">
                <Text
                  fontSize={13}
                  fontWeight="600"
                  color={colors.text}
                  fontFamily="$body"
                >
                  Phone Number *
                </Text>
                <Input
                  placeholder="Enter phone number"
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  keyboardType="phone-pad"
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderRadius={borderRadius.input}
                  paddingHorizontal={spacing.md}
                  paddingVertical={spacing.sm}
                  fontSize={15}
                  height={44}
                  borderBottomWidth={0}
                  style={{
                    borderBottomWidth: 0,
                    borderBottomColor: 'transparent',
                  }}
                  focusStyle={{
                    borderColor: colors.primary,
                    backgroundColor: colors.card,
                  }}
                />
              </YStack>
            </YStack>
          </Card>

          {/* Bill Information */}
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.large}
            padding={spacing.lg}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
            style={shadows.small}
          >
            <Text
              fontSize={16}
              fontWeight="700"
              color={colors.text}
              fontFamily="$heading"
              marginBottom="$4"
              letterSpacing={-0.3}
            >
              Bill Information
            </Text>

            <YStack space="$3">
              {/* Bill Number and Amount Row */}
              <XStack space="$3">
                <YStack space="$2" flex={1}>
                  <Text
                    fontSize={13}
                    fontWeight="600"
                    color={colors.text}
                    fontFamily="$body"
                  >
                    Bill Number *
                  </Text>
                  <Input
                    placeholder="Bill #"
                    value={billNumber}
                    onChangeText={setBillNumber}
                    backgroundColor={colors.backgroundAlt}
                    borderColor={colors.border}
                    borderRadius={borderRadius.input}
                    paddingHorizontal={spacing.md}
                    paddingVertical={spacing.sm}
                    fontSize={15}
                    height={44}
                    borderBottomWidth={0}
                    style={{
                      borderBottomWidth: 0,
                      borderBottomColor: 'transparent',
                    }}
                    focusStyle={{
                      borderColor: colors.primary,
                      backgroundColor: colors.card,
                    }}
                  />
                </YStack>

                <YStack space="$2" flex={1}>
                  <Text
                    fontSize={13}
                    fontWeight="600"
                    color={colors.text}
                    fontFamily="$body"
                  >
                    Amount *
                  </Text>
                  <Input
                    placeholder="₹ Amount"
                    value={amount}
                    onChangeText={setAmount}
                    keyboardType="numeric"
                    backgroundColor={colors.backgroundAlt}
                    borderColor={colors.border}
                    borderRadius={borderRadius.input}
                    paddingHorizontal={spacing.md}
                    paddingVertical={spacing.sm}
                    fontSize={15}
                    height={44}
                    borderBottomWidth={0}
                    style={{
                      borderBottomWidth: 0,
                      borderBottomColor: 'transparent',
                    }}
                    focusStyle={{
                      borderColor: colors.primary,
                      backgroundColor: colors.card,
                    }}
                  />
                </YStack>
              </XStack>

              {/* Bill Date */}
              <DatePickerInput
                label="Bill Date"
                value={billDate}
                onDateChange={setBillDate}
                placeholder="Select bill creation date (optional)"
                required={false}
              />

              {/* Delivery Date */}
              <DatePickerInput
                label="Delivery Date *"
                value={deliveryDate}
                onDateChange={setDeliveryDate}
                placeholder="Select delivery date"
                required={true}
              />

              {/* Notes */}
              <YStack space="$2">
                <Text
                  fontSize={13}
                  fontWeight="600"
                  color={colors.text}
                  fontFamily="$body"
                >
                  Notes
                </Text>
                <Input
                  placeholder="Additional notes (optional)"
                  value={notes}
                  onChangeText={setNotes}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderRadius={borderRadius.input}
                  paddingHorizontal={spacing.md}
                  paddingVertical={spacing.sm}
                  fontSize={15}
                  height={44}
                  borderBottomWidth={0}
                  style={{
                    borderBottomWidth: 0,
                    borderBottomColor: 'transparent',
                  }}
                  focusStyle={{
                    borderColor: colors.primary,
                    backgroundColor: colors.card,
                  }}
                />
              </YStack>
            </YStack>
          </Card>

          {/* Action Buttons */}
          <YStack
            paddingHorizontal={spacing.lg}
            paddingTop={spacing.lg}
            paddingBottom={spacing.md}
            space="$3"
          >
            <Button
              onPress={handleSubmit}
              disabled={loading || uploadingImage}
              backgroundColor={colors.primary}
              borderRadius={borderRadius.large}
              height={56}
              pressStyle={{
                backgroundColor: colors.primaryDark,
                scale: 0.98,
              }}
              opacity={loading || uploadingImage ? 0.7 : 1}
              style={shadows.button}
            >
              <XStack alignItems="center" gap="$2">
                <SaveIcon size={18} color="white" />
                <Text
                  color="white"
                  fontSize={16}
                  fontWeight="700"
                  fontFamily="$heading"
                  letterSpacing={-0.3}
                  textAlign="center"
                >
                  {uploadingImage ? 'Uploading Image...' : loading ? 'Creating Bill...' : 'Create Bill'}
                </Text>
              </XStack>
            </Button>

            <Button
              onPress={() => navigation.goBack()}
              backgroundColor="transparent"
              borderColor={colors.border}
              borderWidth={1}
              borderRadius={borderRadius.large}
              height={52}
              pressStyle={{
                backgroundColor: colors.backgroundAlt,
                scale: 0.98,
              }}
            >
              <Text
                color={colors.textSecondary}
                fontSize={15}
                fontWeight="600"
                fontFamily="$body"
                textAlign="center"
              >
                Cancel
              </Text>
            </Button>
          </YStack>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Image Zoom Modal */}
      {imageUri && (
        <ImageZoomModal
          visible={imageZoomVisible}
          imageUrl={imageUri}
          onClose={() => setImageZoomVisible(false)}
        />
      )}


    </YStack>
  );
};

export default AddBillScreen;
