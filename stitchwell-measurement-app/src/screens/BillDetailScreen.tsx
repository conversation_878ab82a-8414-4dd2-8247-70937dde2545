import React, { useState, useEffect } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H2,
  XStack,
  YStack,
  Text,
  Card,
  Separator,
} from 'tamagui';
import {
  ScrollView,
  View,
  Alert,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  ActionSheetIOS,
  Platform
} from 'react-native';
import { Measurement } from '../types';
import { colors, borderRadius, spacing, shadows } from '../theme/colors';
import { getMeasurementById, deleteMeasurement } from '../services/measurementService';
import {
  UserIcon,
  PhoneIcon,
  BillIcon,
  CalendarIcon,
  FileTextIcon,
  EditIcon,
  TrashIcon,
  BackIcon,
  CallIcon,
  ImageIcon,
  ZoomIcon
} from '../components/TabIcons';
import ImageZoomModal from '../components/ImageZoomModal';
import AspectRatioImage from '../components/AspectRatioImage';
import GradientWrapper from '../components/GradientWrapper';

type Props = NativeStackScreenProps<RootStackParamList, 'BillDetail'>;

const BillDetailScreen = ({ navigation, route }: Props) => {
  const { billId } = route.params;
  const [bill, setBill] = useState<Measurement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageZoomVisible, setImageZoomVisible] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadBillDetails();
  }, [billId]);

  const loadBillDetails = async () => {
    try {
      setLoading(true);
      const billData = await getMeasurementById(billId);
      setBill(billData);
      setError(null);
    } catch (err) {
      console.error('Error loading bill details:', err);
      setError('Failed to load bill details');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('EditBill', { billId });
  };

  const handleDelete = () => {
    const showDeleteConfirmation = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Delete Bill'],
            destructiveButtonIndex: 1,
            cancelButtonIndex: 0,
            title: 'Are you sure you want to delete this bill?',
            message: 'This action cannot be undone.',
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              confirmDelete();
            }
          }
        );
      } else {
        Alert.alert(
          'Delete Bill',
          'Are you sure you want to delete this bill? This action cannot be undone.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Delete', style: 'destructive', onPress: confirmDelete },
          ]
        );
      }
    };

    const confirmDelete = async () => {
      try {
        setDeleting(true);
        await deleteMeasurement(billId);

        // Success feedback
        Alert.alert(
          '✅ Success',
          `Bill ${bill?.billNo ? `#${bill.billNo}` : ''} has been deleted successfully.`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } catch (err) {
        console.error('Error deleting bill:', err);

        // Error feedback with more details
        const errorMessage = err?.message || 'Unknown error occurred';
        Alert.alert(
          '❌ Delete Failed',
          `Failed to delete the bill. Please check your internet connection and try again.\n\nError: ${errorMessage}`,
          [
            { text: 'Retry', onPress: confirmDelete },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      } finally {
        setDeleting(false);
      }
    };

    showDeleteConfirmation();
  };

  const handleCall = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Alert.alert(
      'Call Customer',
      `Do you want to call ${phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Call', onPress: () => {
          // In a real app, you would use Linking.openURL(url)
          console.log('Calling:', phoneNumber);
        }},
      ]
    );
  };

  if (loading) {
    return (
      <YStack f={1} backgroundColor={colors.background} justifyContent="center" alignItems="center">
        <ActivityIndicator size="large" color={colors.primary} />
        <Text marginTop="$4" color={colors.textSecondary}>Loading bill details...</Text>
      </YStack>
    );
  }

  if (error || !bill) {
    return (
      <YStack f={1} backgroundColor={colors.background} justifyContent="center" alignItems="center" padding={spacing.lg}>
        <Text color={colors.error} fontSize={16} textAlign="center" marginBottom="$4">
          {error || 'Bill not found'}
        </Text>
        <Button onPress={() => navigation.goBack()}>
          <Text>Go Back</Text>
        </Button>
      </YStack>
    );
  }

  return (
    <YStack f={1} backgroundColor={colors.background}>
      {/* Modern Gradient Header */}
      <GradientWrapper
        colors={colors.primaryGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          paddingHorizontal: spacing.lg,
          paddingTop: spacing.xl + 10,
          paddingBottom: spacing.lg,
          borderBottomLeftRadius: borderRadius.extraLarge,
          borderBottomRightRadius: borderRadius.extraLarge,
          ...shadows.large,
        }}
      >
        <XStack alignItems="center" justifyContent="space-between">
          <XStack alignItems="center" space="$3" flex={1}>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: 'rgba(255,255,255,0.2)',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 1,
                borderColor: 'rgba(255,255,255,0.3)',
              }}
            >
              <BackIcon size={18} color="white" />
            </TouchableOpacity>
            <YStack flex={1}>
              <H2
                color="white"
                fontSize={20}
                fontWeight="700"
                fontFamily="$heading"
                letterSpacing={-0.5}
                marginBottom="$1"
              >
                Bill Details
              </H2>
              <Text
                color="rgba(255,255,255,0.9)"
                fontSize={13}
                fontFamily="$body"
                fontWeight="500"
              >
                {bill.billNo ? `Bill #${bill.billNo}` : `ID: ${bill.id.substring(0, 8)}`}
              </Text>
            </YStack>
          </XStack>
        </XStack>
      </GradientWrapper>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: spacing.xl }}
      >
        {/* Action Icons */}
        <XStack
          paddingHorizontal={spacing.lg}
          paddingTop={spacing.md}
          paddingBottom={spacing.md}
          justifyContent="flex-end"
          space="$3"
        >
          <TouchableOpacity
            onPress={deleting ? undefined : handleEdit}
            style={{
              width: 44,
              height: 44,
              borderRadius: 22,
              backgroundColor: deleting ? colors.textSecondary : colors.primary,
              justifyContent: 'center',
              alignItems: 'center',
              ...shadows.button,
              opacity: deleting ? 0.6 : 1,
            }}
            activeOpacity={deleting ? 1 : 0.8}
            disabled={deleting}
          >
            <EditIcon size={20} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={deleting ? undefined : handleDelete}
            style={{
              width: 44,
              height: 44,
              borderRadius: 22,
              backgroundColor: deleting ? colors.textSecondary : colors.error,
              justifyContent: 'center',
              alignItems: 'center',
              ...shadows.button,
              opacity: deleting ? 0.6 : 1,
            }}
            activeOpacity={deleting ? 1 : 0.8}
            disabled={deleting}
          >
            {deleting ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <TrashIcon size={20} color="white" />
            )}
          </TouchableOpacity>
        </XStack>

        {/* Customer Information */}
        <Card
          backgroundColor={colors.card}
          borderRadius={borderRadius.large}
          padding={spacing.lg}
          marginHorizontal={spacing.lg}
          marginTop={-spacing.md}
          borderWidth={1}
          borderColor={colors.border}
          style={shadows.medium}
        >
          <Text
            fontSize={16}
            fontWeight="700"
            color={colors.text}
            fontFamily="$heading"
            marginBottom="$4"
            letterSpacing={-0.3}
          >
            Customer Information
          </Text>

          <YStack space="$3">
            {/* Customer Name */}
            <XStack alignItems="center" space="$3">
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: `${colors.primary}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <UserIcon size={20} color={colors.primary} />
              </View>
              <YStack flex={1}>
                <Text
                  color={colors.text}
                  fontWeight="600"
                  fontSize={16}
                  fontFamily="$heading"
                >
                  {bill.name}
                </Text>
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                >
                  Customer Name
                </Text>
              </YStack>
            </XStack>

            <Separator />

            {/* Phone Number */}
            <XStack alignItems="center" justifyContent="space-between">
              <XStack alignItems="center" space="$3" flex={1}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: `${colors.primary}15`,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                  <PhoneIcon size={20} color={colors.primary} />
                </View>
                <YStack flex={1}>
                  <Text
                    color={colors.text}
                    fontWeight="600"
                    fontSize={16}
                    fontFamily="$heading"
                  >
                    {bill.phoneNo}
                  </Text>
                  <Text
                    color={colors.textSecondary}
                    fontSize={14}
                    fontFamily="$body"
                  >
                    Phone Number
                  </Text>
                </YStack>
              </XStack>
              {bill.phoneNo && (
                <TouchableOpacity
                  onPress={() => handleCall(bill.phoneNo)}
                  style={{
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: colors.accent,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <CallIcon size={16} color="white" />
                </TouchableOpacity>
              )}
            </XStack>
          </YStack>
        </Card>

        {/* Bill Information */}
        <Card
          backgroundColor={colors.card}
          borderRadius={borderRadius.large}
          padding={spacing.lg}
          marginHorizontal={spacing.lg}
          marginTop={spacing.md}
          borderWidth={1}
          borderColor={colors.border}
          style={shadows.small}
        >
          <Text
            fontSize={16}
            fontWeight="700"
            color={colors.text}
            fontFamily="$heading"
            marginBottom="$4"
            letterSpacing={-0.3}
          >
            Bill Information
          </Text>

          <YStack space="$3">
            {/* Bill Number */}
            <XStack alignItems="center" space="$3">
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: `${colors.secondary}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <BillIcon size={20} color={colors.secondary} />
              </View>
              <YStack flex={1}>
                <Text
                  color={colors.text}
                  fontWeight="600"
                  fontSize={16}
                  fontFamily="$heading"
                >
                  {bill.billNo || 'Not specified'}
                </Text>
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                >
                  Bill Number
                </Text>
              </YStack>
            </XStack>

            <Separator />

            {/* Delivery Date */}
            <XStack alignItems="center" space="$3">
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: `${colors.secondary}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <CalendarIcon size={20} color={colors.secondary} />
              </View>
              <YStack flex={1}>
                <Text
                  color={colors.text}
                  fontWeight="600"
                  fontSize={16}
                  fontFamily="$heading"
                >
                  {bill.deliveryDate || 'Not specified'}
                </Text>
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                >
                  Delivery Date
                </Text>
              </YStack>
            </XStack>
          </YStack>
        </Card>

        {/* Bill Image */}
        {bill.imageUrl && (
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.large}
            padding={spacing.md}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
            style={shadows.small}
          >
            <XStack alignItems="center" justifyContent="space-between" marginBottom="$3">
              <Text
                fontSize={15}
                fontWeight="700"
                color={colors.text}
                fontFamily="$heading"
                letterSpacing={-0.3}
              >
                Bill Image
              </Text>
              <TouchableOpacity
                onPress={() => setImageZoomVisible(true)}
                style={{
                  padding: 6,
                  borderRadius: 12,
                  backgroundColor: `${colors.primary}15`,
                }}
              >
                <ZoomIcon size={14} color={colors.primary} />
              </TouchableOpacity>
            </XStack>
            <AspectRatioImage
              source={{ uri: bill.imageUrl }}
              onPress={() => setImageZoomVisible(true)}
              showZoomIcon={true}
              borderRadius={borderRadius.medium}
              backgroundColor={colors.backgroundAlt}
              height={160}
            />
          </Card>
        )}

        {/* Notes */}
        {bill.notes && (
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.large}
            padding={spacing.lg}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
            style={shadows.small}
          >
            <XStack alignItems="center" space="$2" marginBottom="$3">
              <View style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: `${colors.accent}15`,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <FileTextIcon size={16} color={colors.accent} />
              </View>
              <Text
                fontSize={15}
                fontWeight="700"
                color={colors.text}
                fontFamily="$heading"
                letterSpacing={-0.3}
              >
                Additional Notes
              </Text>
            </XStack>
            <Text
              color={colors.text}
              fontSize={14}
              fontFamily="$body"
              lineHeight={20}
              paddingLeft={spacing.xl}
            >
              {bill.notes}
            </Text>
          </Card>
        )}


      </ScrollView>

      {/* Image Zoom Modal */}
      {bill?.imageUrl && (
        <ImageZoomModal
          visible={imageZoomVisible}
          imageUrl={bill.imageUrl}
          onClose={() => setImageZoomVisible(false)}
        />
      )}
    </YStack>
  );
};

export default BillDetailScreen;
