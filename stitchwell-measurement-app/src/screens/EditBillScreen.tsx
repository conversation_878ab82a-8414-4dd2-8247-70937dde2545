import React, { useState, useEffect } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H2,
  XStack,
  YStack,
  Input,
  Text,
  Card,
} from 'tamagui';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  TouchableOpacity,
  ActionSheetIOS,
  ActivityIndicator
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Measurement } from '../types';
import { colors, borderRadius, spacing, shadows } from '../theme/colors';
import { getMeasurementById, updateMeasurement } from '../services/measurementService';
import { uploadImage } from '../services/storageService';
import {
  SaveIcon,
  CameraIcon,
} from '../components/TabIcons';
import ImageZoomModal from '../components/ImageZoomModal';
import DatePickerInput from '../components/DatePickerInput';
import AspectRatioImage from '../components/AspectRatioImage';
import GradientWrapper from '../components/GradientWrapper';

type Props = NativeStackScreenProps<RootStackParamList, 'EditBill'>;

const EditBillScreen = ({ navigation, route }: Props) => {
  const { billId } = route.params;
  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [billNumber, setBillNumber] = useState('');
  const [deliveryDate, setDeliveryDate] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [imageZoomVisible, setImageZoomVisible] = useState(false);

  useEffect(() => {
    loadBillData();
  }, [billId]);

  const loadBillData = async () => {
    try {
      setInitialLoading(true);
      const billData = await getMeasurementById(billId) as any;

      // Populate form fields
      setCustomerName(billData.name || '');
      setPhoneNumber(billData.phoneNo || '');
      setBillNumber(billData.billNo || '');
      setDeliveryDate(billData.deliveryDate || '');
      setNotes(billData.notes || '');
      setImageUri(billData.imageUrl || null);
    } catch (error) {
      console.error('Error loading bill data:', error);
      Alert.alert('Error', 'Failed to load bill data');
      navigation.goBack();
    } finally {
      setInitialLoading(false);
    }
  };

  // Request camera permissions
  React.useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Sorry, we need camera roll permissions to upload images.');
      }
    })();
  }, []);

  const pickImage = async () => {
    const showImagePicker = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
            cancelButtonIndex: 0,
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              openCamera();
            } else if (buttonIndex === 2) {
              openGallery();
            }
          }
        );
      } else {
        Alert.alert(
          'Select Image',
          'Choose an option',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Take Photo', onPress: openCamera },
            { text: 'Choose from Gallery', onPress: openGallery },
          ]
        );
      }
    };

    const openCamera = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Camera permission is required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Allow natural aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    const openGallery = async () => {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false, // Allow natural aspect ratio
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setImageUri(result.assets[0].uri);
      }
    };

    showImagePicker();
  };

  const handleRemoveImage = () => {
    Alert.alert(
      'Remove Image',
      'Are you sure you want to remove the selected image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => setImageUri(null)
        },
      ]
    );
  };

  const validateForm = () => {
    // Customer name is no longer required - allow bills without customer names
    if (!phoneNumber.trim()) {
      Alert.alert('Validation Error', 'Phone number is required');
      return false;
    }
    if (!billNumber.trim()) {
      Alert.alert('Validation Error', 'Bill number is required');
      return false;
    }
    if (!deliveryDate.trim()) {
      Alert.alert('Validation Error', 'Delivery date is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      let uploadedImageUrl = imageUri;
      
      // Upload new image if it's a local URI (not a URL)
      if (imageUri && !imageUri.startsWith('http')) {
        setUploadingImage(true);
        try {
          uploadedImageUrl = await uploadImage(imageUri);
        } catch (imageError) {
          console.error('Error uploading image:', imageError);
          Alert.alert('Warning', 'Failed to upload image, but bill will be updated without it.');
        } finally {
          setUploadingImage(false);
        }
      }

      // Update the measurement record
      const updatedBill: Partial<Measurement> = {
        name: customerName.trim() || 'Unknown Customer', // Provide default if customer name is empty
        phoneNo: phoneNumber.trim(),
        billNo: billNumber.trim(),
        deliveryDate: deliveryDate.trim(),
        imageUrl: uploadedImageUrl || '',
        notes: notes.trim(),
      };

      await updateMeasurement(billId, updatedBill);
      
      Alert.alert(
        'Success', 
        'Bill updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error updating bill:', error);
      Alert.alert('Error', 'Failed to update bill. Please try again.');
    } finally {
      setLoading(false);
      setUploadingImage(false);
    }
  };

  if (initialLoading) {
    return (
      <YStack f={1} backgroundColor={colors.background} justifyContent="center" alignItems="center">
        <ActivityIndicator size="large" color={colors.primary} />
        <Text marginTop="$4" color={colors.textSecondary}>Loading bill data...</Text>
      </YStack>
    );
  }

  return (
    <YStack f={1} backgroundColor={colors.background}>
      {/* Modern Gradient Header */}
      <GradientWrapper
        colors={colors.primaryGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          paddingHorizontal: spacing.lg,
          paddingTop: spacing.xl + 10,
          paddingBottom: spacing.lg,
          borderBottomLeftRadius: borderRadius.extraLarge,
          borderBottomRightRadius: borderRadius.extraLarge,
          ...shadows.large,
        }}
      >
        <XStack alignItems="center" justifyContent="space-between" marginBottom="$2">
          <YStack flex={1}>
            <H2
              color="white"
              fontSize={22}
              fontWeight="700"
              fontFamily="$heading"
              letterSpacing={-0.5}
              marginBottom="$1"
            >
              Edit Bill
            </H2>
            <Text
              color="rgba(255,255,255,0.9)"
              fontSize={13}
              fontFamily="$body"
              fontWeight="500"
            >
              Update billing information
            </Text>
          </YStack>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              width: 36,
              height: 36,
              borderRadius: 18,
              backgroundColor: 'rgba(255,255,255,0.2)',
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.3)',
            }}
          >
            <Text fontSize={16} color="white" fontWeight="600">✕</Text>
          </TouchableOpacity>
        </XStack>
      </GradientWrapper>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: spacing.xl }}
        >
          {/* Quick Actions Section */}
          {!imageUri && (
            <Card
              backgroundColor={colors.card}
              borderRadius={borderRadius.large}
              marginHorizontal={spacing.lg}
              marginTop={-spacing.md}
              padding={spacing.lg}
              borderWidth={1}
              borderColor={colors.border}
              style={shadows.medium}
            >
              <Button
                onPress={pickImage}
                backgroundColor={colors.accent}
                borderRadius={borderRadius.medium}
                paddingVertical={spacing.sm}
                height={44}
                pressStyle={{
                  backgroundColor: colors.accentDark,
                  scale: 0.98,
                }}
              >
                <XStack alignItems="center" gap="$2">
                  <CameraIcon size={16} color="white" />
                  <Text color="white" fontSize={13} fontWeight="600">
                    Add Bill Image
                  </Text>
                </XStack>
              </Button>
            </Card>
          )}

          {/* Image Preview Section */}
          {imageUri && (
            <Card
              backgroundColor={colors.card}
              borderRadius={borderRadius.large}
              padding={spacing.md}
              marginHorizontal={spacing.lg}
              marginTop={-spacing.md}
              borderWidth={1}
              borderColor={colors.border}
              style={shadows.small}
            >
              <AspectRatioImage
                source={{ uri: imageUri }}
                onPress={() => setImageZoomVisible(true)}
                showZoomIcon={true}
                borderRadius={borderRadius.medium}
                backgroundColor={colors.backgroundAlt}
                height={160}
              />
              <XStack gap="$2" marginTop="$3">
                <Button
                  flex={1}
                  onPress={pickImage}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderWidth={1}
                  borderRadius={borderRadius.small}
                  paddingVertical={spacing.xs}
                  height={36}
                >
                  <XStack alignItems="center" gap="$1">
                    <CameraIcon size={14} color={colors.textSecondary} />
                    <Text color={colors.textSecondary} fontSize={12} fontWeight="500">Change</Text>
                  </XStack>
                </Button>
                <Button
                  flex={1}
                  onPress={handleRemoveImage}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderWidth={1}
                  borderRadius={borderRadius.small}
                  paddingVertical={spacing.xs}
                  height={36}
                >
                  <Text color={colors.textSecondary} fontSize={12} fontWeight="500">Remove</Text>
                </Button>
              </XStack>
            </Card>
          )}

          {/* Customer Information */}
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.large}
            padding={spacing.lg}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
            style={shadows.small}
          >
            <Text
              fontSize={16}
              fontWeight="700"
              color={colors.text}
              fontFamily="$heading"
              marginBottom="$4"
              letterSpacing={-0.3}
            >
              Customer Details
            </Text>

            <YStack gap="$3">
              {/* Customer Name */}
              <YStack gap="$2">
                <Text
                  fontSize={13}
                  fontWeight="600"
                  color={colors.text}
                  fontFamily="$body"
                >
                  Customer Name
                </Text>
                <Input
                  placeholder="Enter customer name"
                  value={customerName}
                  onChangeText={setCustomerName}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderRadius={borderRadius.input}
                  paddingHorizontal={spacing.md}
                  paddingVertical={spacing.sm}
                  fontSize={15}
                  height={44}
                  borderBottomWidth={0}
                  style={{
                    borderBottomWidth: 0,
                    borderBottomColor: 'transparent',
                  }}
                  focusStyle={{
                    borderColor: colors.primary,
                    backgroundColor: colors.card,
                  }}
                />
              </YStack>

              {/* Phone Number */}
              <YStack gap="$2">
                <Text
                  fontSize={13}
                  fontWeight="600"
                  color={colors.text}
                  fontFamily="$body"
                >
                  Phone Number *
                </Text>
                <Input
                  placeholder="Enter phone number"
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  keyboardType="phone-pad"
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderRadius={borderRadius.input}
                  paddingHorizontal={spacing.md}
                  paddingVertical={spacing.sm}
                  fontSize={15}
                  height={44}
                  borderBottomWidth={0}
                  style={{
                    borderBottomWidth: 0,
                    borderBottomColor: 'transparent',
                  }}
                  focusStyle={{
                    borderColor: colors.primary,
                    backgroundColor: colors.card,
                  }}
                />
              </YStack>
            </YStack>
          </Card>

          {/* Bill Information */}
          <Card
            backgroundColor={colors.card}
            borderRadius={borderRadius.large}
            padding={spacing.lg}
            marginHorizontal={spacing.lg}
            marginTop={spacing.md}
            borderWidth={1}
            borderColor={colors.border}
            style={shadows.small}
          >
            <Text
              fontSize={16}
              fontWeight="700"
              color={colors.text}
              fontFamily="$heading"
              marginBottom="$4"
              letterSpacing={-0.3}
            >
              Bill Information
            </Text>

            <YStack gap="$3">
              {/* Bill Number */}
              <YStack gap="$2">
                <Text
                  fontSize={13}
                  fontWeight="600"
                  color={colors.text}
                  fontFamily="$body"
                >
                  Bill Number *
                </Text>
                <Input
                  placeholder="Enter bill number"
                  value={billNumber}
                  onChangeText={setBillNumber}
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderRadius={borderRadius.input}
                  paddingHorizontal={spacing.md}
                  paddingVertical={spacing.sm}
                  fontSize={15}
                  height={44}
                  borderBottomWidth={0}
                  style={{
                    borderBottomWidth: 0,
                    borderBottomColor: 'transparent',
                  }}
                  focusStyle={{
                    borderColor: colors.primary,
                    backgroundColor: colors.card,
                  }}
                />
              </YStack>

              {/* Delivery Date */}
              <DatePickerInput
                label="Delivery Date *"
                value={deliveryDate}
                onDateChange={setDeliveryDate}
                placeholder="Select delivery date"
                required={true}
              />
            </YStack>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Action Buttons */}
      <YStack
        paddingHorizontal={spacing.lg}
        paddingTop={spacing.lg}
        paddingBottom={spacing.md}
        gap="$3"
      >
        <Button
          onPress={handleSubmit}
          disabled={loading || uploadingImage}
          backgroundColor={colors.primary}
          borderRadius={borderRadius.large}
          height={56}
          pressStyle={{
            backgroundColor: colors.primaryDark,
            scale: 0.98,
          }}
          opacity={loading || uploadingImage ? 0.7 : 1}
          style={shadows.button}
        >
          <XStack alignItems="center" gap="$2">
            <SaveIcon size={18} color="white" />
            <Text
              color="white"
              fontSize={16}
              fontWeight="700"
              fontFamily="$heading"
              letterSpacing={-0.3}
              textAlign="center"
            >
              {uploadingImage ? 'Uploading Image...' : loading ? 'Updating Bill...' : 'Update Bill'}
            </Text>
          </XStack>
        </Button>

        <Button
          onPress={() => navigation.goBack()}
          backgroundColor="transparent"
          borderColor={colors.border}
          borderWidth={1}
          borderRadius={borderRadius.large}
          height={52}
          pressStyle={{
            backgroundColor: colors.backgroundAlt,
            scale: 0.98,
          }}
        >
          <Text
            color={colors.textSecondary}
            fontSize={15}
            fontWeight="600"
            fontFamily="$body"
            textAlign="center"
          >
            Cancel
          </Text>
        </Button>
      </YStack>

      {/* Image Zoom Modal */}
      {imageUri && (
        <ImageZoomModal
          visible={imageZoomVisible}
          imageUrl={imageUri}
          onClose={() => setImageZoomVisible(false)}
        />
      )}
    </YStack>
  );
};

export default EditBillScreen;
