import React, { useState, useEffect } from 'react';
import { FlatList, View, ActivityIndicator, Linking, Alert, TextInput } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { useFocusEffect } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H1,
  XStack,
  YStack,
  Card,
  Text,
} from 'tamagui';
import {
  UserIcon,
  SearchIcon,
  CalendarIcon,
  FileTextIcon,
  TrashIcon,
  CallIcon
} from '../components/TabIcons';
import GradientWrapper from '../components/GradientWrapper';
import FloatingActionButton from '../components/FloatingActionButton';
import SkeletonLoader from '../components/SkeletonLoader';
import { Measurement } from '../types/index';
import { colors, shadows, borderRadius, spacing } from '../theme/colors';
import { subscribeMeasurements, deleteMeasurement, searchAllMeasurements, getTotalMeasurementsCount } from '../services/measurementService';
import {
  UI_CONSTANTS,
  TEXT_CONSTANTS,
  BUSINESS_CONSTANTS,
  DELIVERY_STATUS,
  getCurrentMonthRange
} from '../constants';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen = ({ navigation }: Props) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [measurements, setMeasurements] = useState<Measurement[]>([]);
  const [searchResults, setSearchResults] = useState<Measurement[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number | null>(null);
  const [loadingMore, setLoadingMore] = useState(false);
  const [allLoaded, setAllLoaded] = useState(false);


  // Define types for the subscription functions
  type SubscriptionFunctions = {
    unsubscribe: () => void;
    loadMore: () => void;
  };

  // Reference to store the subscription function
  const measurementsRef = React.useRef<SubscriptionFunctions | null>(null);

  // Handle pull-to-refresh
  const handleRefresh = () => {
    setRefreshing(true);
    // The real-time listener will update the data
    // Just reset the refreshing state after a short delay
    setTimeout(() => {
      setRefreshing(false);
    }, BUSINESS_CONSTANTS.REFRESH_TIMEOUT);
  };

  // Handle delete measurement with confirmation
  const handleDelete = (id: string, customerName: string) => {
    Alert.alert(
      'Delete Bill',
      `Are you sure you want to delete the bill for ${customerName}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => confirmDelete(id)
        },
      ]
    );
  };

  // Confirm and execute delete
  const confirmDelete = async (id: string) => {
    try {
      await deleteMeasurement(id);
      // No need to update state as the listener will handle it
    } catch (error) {
      console.error('Error deleting measurement:', error);
      setError('Failed to delete measurement. Please try again.');
    }
  };

  // Handle phone call
  const handleCall = async (phoneNumber: string) => {
    try {
      const phoneUrl = `tel:${phoneNumber}`;
      const canOpen = await Linking.canOpenURL(phoneUrl);

      if (canOpen) {
        await Linking.openURL(phoneUrl);
      } else {
        Alert.alert('Error', 'Unable to make phone calls on this device');
      }
    } catch (error) {
      console.error('Error making phone call:', error);
      Alert.alert('Error', 'Failed to initiate phone call');
    }
  };

  // Get total count on component mount
  useEffect(() => {
    const fetchTotalCount = async () => {
      try {
        const count = await getTotalMeasurementsCount();
        setTotalCount(count);
      } catch (error) {
        console.error('❌ Error fetching total count:', error);
      }
    };

    fetchTotalCount();
  }, []);

  // Subscribe to real-time updates from Firestore with lazy loading
  useEffect(() => {
    let isSubscribed = true; // Flag to prevent state updates after unmount

    const initializeData = async () => {
      // Prevent multiple initializations
      if (measurementsRef.current) {
        console.log('🚫 Subscription already exists, skipping initialization');
        return;
      }

      setLoading(true);
      setError(null);

      console.log('🔄 Initializing measurements data...');

      try {
        // Set up listener to load measurements with pagination
        const subscription = subscribeMeasurements(
          (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean, hasMore: boolean }) => {
            // Only update state if component is still mounted
            if (!isSubscribed) {
              console.log('🚫 Component unmounted, ignoring subscription update');
              return;
            }

            console.log(`📱 Updating UI with measurements: ${updatedMeasurements.length} loaded${totalCount ? ` / ${totalCount} total` : ''}`);
            setMeasurements(updatedMeasurements);
            setLoading(false);
            setLoadingMore(paginationInfo.isLoading);
            setAllLoaded(paginationInfo.allLoaded);
            setError(null);
          },
          20, // Page size
          (error: Error) => {
            // Only update state if component is still mounted
            if (!isSubscribed) {
              console.log('🚫 Component unmounted, ignoring subscription error');
              return;
            }

            console.error('❌ Error in measurements subscription:', error);
            let errorMessage = 'Failed to load measurements. Please try again.';

            if (error.message.includes('timeout')) {
              errorMessage = 'Connection timeout. Please check your internet connection and try again.';
            } else if (error.message.includes('permission')) {
              errorMessage = 'Permission denied. Please check your database access.';
            } else if (error.message.includes('network')) {
              errorMessage = 'Network error. Please check your internet connection.';
            }

            setError(errorMessage);
            setLoading(false);
            setLoadingMore(false);
          }
        ) as SubscriptionFunctions;

        // Store the subscription functions in the ref for later use
        measurementsRef.current = subscription;
      } catch (error) {
        console.error('❌ Error initializing subscription:', error);
        if (isSubscribed) {
          setError('Failed to initialize data connection');
          setLoading(false);
        }
      }
    };

    initializeData();

    // Cleanup subscription on unmount
    return () => {
      isSubscribed = false;
      if (measurementsRef.current) {
        console.log('🧹 Cleaning up measurements subscription');
        measurementsRef.current.unsubscribe();
        measurementsRef.current = null;
      }
    };
  }, []); // Run once on mount for lazy loading

  // Effect to handle optimized search with progressive results
  useEffect(() => {
    const performOptimizedSearch = async () => {
      if (searchQuery.trim() === '') {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      setSearchResults([]); // Clear previous results

      try {
        console.log('🔍 Performing optimized search for:', searchQuery);

        // Use progressive search with callback for immediate results
        const results = await searchAllMeasurements(
          searchQuery,
          (partialResults: Measurement[], isComplete: boolean) => {
            // Update UI with progressive results
            setSearchResults(partialResults);
            if (isComplete) {
              setIsSearching(false);
              console.log('✅ Search completed, found:', partialResults.length, 'results');
            } else {
              console.log('🔄 Progressive results:', partialResults.length, 'so far...');
            }
          }
        );

        // Final results (in case progressive callback wasn't used)
        setSearchResults(results);
        console.log('✅ Search completed, found:', results.length, 'results');
      } catch (error) {
        console.error('❌ Search error:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    // Smart debouncing - shorter delay for cached results, longer for new searches
    const isLikelyFromCache = searchQuery.length >= BUSINESS_CONSTANTS.SEARCH_MIN_LENGTH_FOR_CACHE;
    const debounceDelay = isLikelyFromCache ? BUSINESS_CONSTANTS.SEARCH_DEBOUNCE_CACHED : BUSINESS_CONSTANTS.SEARCH_DEBOUNCE_NEW;

    const timeoutId = setTimeout(performOptimizedSearch, debounceDelay);
    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Handle screen focus to refresh data when returning from AddBillScreen
  useFocusEffect(
    React.useCallback(() => {
      console.log('🔄 HomeScreen focused - refreshing total count...');

      // Refresh total count when screen comes into focus
      const refreshTotalCount = async () => {
        try {
          const count = await getTotalMeasurementsCount();
          setTotalCount(count);
          console.log(`📊 Updated total measurements count: ${count}`);
        } catch (error) {
          console.error('❌ Error refreshing total count:', error);
        }
      };

      refreshTotalCount();
    }, [])
  );

  // Filter and sort measurements based on search query
  const filteredMeasurements = React.useMemo(() => {
    // If user is searching, use search results from entire database
    if (searchQuery.trim() !== '') {
      // Return search results (already sorted by deliveryDate priority in searchAllMeasurements)
      return searchResults;
    }

    // If not searching, use all loaded measurements sorted by delivery date (upcoming first)
    let filtered = measurements;

    // Sort all measurements by delivery date priority (upcoming deliveries first)
    return filtered.sort((a, b) => {
      // Helper function to parse DD-MM-YYYY date format
      const parseDate = (dateStr: string) => {
        if (!dateStr) return null;
        const [day, month, year] = dateStr.split('-');
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      };

      const deliveryA = parseDate(a.deliveryDate);
      const deliveryB = parseDate(b.deliveryDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time for accurate date comparison

      // Categorize deliveries
      const getDeliveryPriority = (deliveryDate: Date | null) => {
        if (!deliveryDate) return 3; // No delivery date - lowest priority

        if (deliveryDate >= today) {
          return 1; // Future/upcoming deliveries - highest priority
        } else {
          return 2; // Overdue deliveries - medium priority
        }
      };

      const priorityA = getDeliveryPriority(deliveryA);
      const priorityB = getDeliveryPriority(deliveryB);

      // Sort by priority first
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // Within same priority, sort by specific rules
      if (priorityA === 1) {
        // For upcoming deliveries: sort by delivery date (earliest upcoming first)
        return deliveryA!.getTime() - deliveryB!.getTime();
      }

      if (priorityA === 2) {
        // For overdue deliveries: sort by delivery date (most recent overdue first)
        return deliveryB!.getTime() - deliveryA!.getTime();
      }

      if (priorityA === 3) {
        // For items without delivery dates: sort by creation timestamp (newest first)
        const timestampA = a.timestamp || 0;
        const timestampB = b.timestamp || 0;
        return timestampB - timestampA;
      }

      return 0;
    });
  }, [measurements, searchQuery, searchResults]);

  // Render each measurement item
  const renderMeasurementItem = ({ item, index }: { item: Measurement, index: number }) => {
    // Get the first three measurements to display as preview
    const measurementEntries = item.measurements
      ? Object.entries(item.measurements)
          .filter(([_, value]) => value !== undefined && value !== null)
          .slice(0, 3)
      : [];

    // Determine delivery status for visual indicators (relative to current month)
    const getDeliveryStatus = () => {
      if (!item.deliveryDate) return DELIVERY_STATUS.NONE;

      // Get current month range dynamically
      const { currentMonth, currentMonthEnd } = getCurrentMonthRange();

      const [day, month, year] = item.deliveryDate.split('-');
      const deliveryDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

      if (deliveryDate < currentMonth) return DELIVERY_STATUS.OVERDUE;
      if (deliveryDate >= currentMonth && deliveryDate <= currentMonthEnd) return DELIVERY_STATUS.CURRENT;
      return DELIVERY_STATUS.FUTURE;
    };

    const deliveryStatus = getDeliveryStatus();

    // Use consistent gradient cycling for all bills
    const gradientKey = `cardGradient${(index % 3) + 1}` as keyof typeof colors;
    const cardGradient = colors[gradientKey] as readonly [string, string, ...string[]];

    // Determine status color for delivery indicators only
    let statusColor: string;
    switch (deliveryStatus) {
      case DELIVERY_STATUS.OVERDUE:
        statusColor = colors.error;
        break;
      case DELIVERY_STATUS.CURRENT:
        statusColor = colors.warning;
        break;
      default:
        statusColor = cardGradient[0];
    }

    return (
      <Card
        elevate
        size="$3"
        marginVertical="$3"
        scale={0.98}
        hoverStyle={{ scale: 1.02 }}
        pressStyle={{ scale: 0.96 }}
        onPress={() => {
          // Navigate to bill detail view
          navigation.navigate('BillDetail', { billId: item.id });
        }}
        style={{
          backgroundColor: colors.card,
          borderRadius: borderRadius.large,
          overflow: 'hidden',
          borderWidth: 1,
          borderColor: colors.border,
          ...shadows.medium,
        }}
      >
        {/* Modern Card Header */}
        <YStack
          style={{
            backgroundColor: `${cardGradient[0]}08`,
            borderBottomWidth: 1,
            borderBottomColor: `${cardGradient[0]}15`,
          }}
        >
          <XStack justifyContent="space-between" alignItems="center" padding="$3">
            <XStack gap="$2" alignItems="center" flex={1}>
              <View
                style={{
                  width: UI_CONSTANTS.AVATAR_SIZE_MEDIUM,
                  height: UI_CONSTANTS.AVATAR_SIZE_MEDIUM,
                  borderRadius: borderRadius.medium,
                  backgroundColor: cardGradient[0],
                  justifyContent: 'center',
                  alignItems: 'center',
                  ...shadows.small,
                }}
              >
                <FileTextIcon size={UI_CONSTANTS.ICON_SIZE_MEDIUM} color="white" />
              </View>
              <YStack flex={1}>
                <Text
                  fontWeight="700"
                  color={colors.text}
                  fontSize={16}
                  fontFamily="$heading"
                  letterSpacing={-0.2}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {item.billNo ? `${TEXT_CONSTANTS.BILL_PREFIX}${item.billNo}` : `${TEXT_CONSTANTS.MEASUREMENT_PREFIX}${item.id.substring(0, BUSINESS_CONSTANTS.ID_DISPLAY_LENGTH)}`}
                </Text>
                <Text
                  color={colors.textSecondary}
                  fontSize={12}
                  fontFamily="$body"
                  fontWeight="500"
                  marginTop="$1"
                >
                  {item.date}
                </Text>
              </YStack>
            </XStack>

            {/* Action Buttons */}
            <XStack gap="$2" alignItems="center">
              {item.phoneNo && (
                <Button
                  size="$2"
                  chromeless
                  circular
                  onPress={(e) => {
                    e.stopPropagation();
                    handleCall(item.phoneNo);
                  }}
                  pressStyle={{ opacity: 0.5 }}
                  hoverStyle={{ opacity: 0.8 }}
                  style={{
                    width: 36,
                    height: 36,
                    backgroundColor: `${colors.accent}15`,
                    borderWidth: 1,
                    borderColor: `${colors.accent}25`,
                  }}
                >
                  <CallIcon size={16} color={colors.accent} />
                </Button>
              )}
              <Button
                size="$2"
                chromeless
                circular
                onPress={(e) => {
                  e.stopPropagation(); // Prevent card onPress from firing
                  handleDelete(item.id, item.name);
                }}
                pressStyle={{ opacity: 0.5 }}
                hoverStyle={{ opacity: 0.8 }}
                style={{
                  width: 36,
                  height: 36,
                  backgroundColor: `${colors.error}15`,
                  borderWidth: 1,
                  borderColor: `${colors.error}25`,
                }}
              >
                <TrashIcon size={16} color={colors.error} />
              </Button>
            </XStack>
          </XStack>
        </YStack>

        {/* Card Content */}
        <YStack padding="$4" gap="$3">
          {/* Customer Information */}
          <XStack gap="$3" alignItems="center">
            <View
              style={{
                width: UI_CONSTANTS.AVATAR_SIZE_LARGE,
                height: UI_CONSTANTS.AVATAR_SIZE_LARGE,
                borderRadius: borderRadius.large,
                backgroundColor: `${cardGradient[0]}12`,
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 2,
                borderColor: `${cardGradient[0]}20`,
              }}
            >
              <UserIcon size={UI_CONSTANTS.ICON_SIZE_LARGE} color={cardGradient[0]} />
            </View>
            <YStack flex={1}>
              <Text
                color={colors.text}
                fontWeight="700"
                fontSize={18}
                fontFamily="$heading"
                lineHeight={22}
                numberOfLines={1}
                ellipsizeMode="tail"
                marginBottom="$1"
              >
                {item.name}
              </Text>
              <XStack gap="$2" alignItems="center">
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="500"
                  lineHeight={18}
                >
                  {item.phoneNo}
                </Text>
                {item.phoneNo && (
                  <>
                    <Text color={colors.textTertiary} fontSize={12}>•</Text>
                    <Text
                      color={colors.textTertiary}
                      fontSize={12}
                      fontFamily="$body"
                      fontWeight="400"
                    >
                      {TEXT_CONSTANTS.TAP_TO_CALL_HINT}
                    </Text>
                  </>
                )}
              </XStack>
            </YStack>
          </XStack>

          {/* Delivery Status Section */}
          {item.deliveryDate && (
            <XStack gap="$3" alignItems="center">
              <View
                style={{
                  width: UI_CONSTANTS.AVATAR_SIZE_MEDIUM,
                  height: UI_CONSTANTS.AVATAR_SIZE_MEDIUM,
                  borderRadius: borderRadius.medium,
                  backgroundColor: `${statusColor}15`,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderWidth: 1,
                  borderColor: `${statusColor}25`,
                }}
              >
                <CalendarIcon size={UI_CONSTANTS.ICON_SIZE_SMALL} color={statusColor} />
              </View>
              <YStack flex={1}>
                <Text
                  color={colors.text}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="600"
                  marginBottom="$1"
                >
                  Delivery Date
                </Text>
                <XStack alignItems="center" gap="$2">
                  <Text
                    color={statusColor}
                    fontSize={14}
                    fontFamily="$body"
                    fontWeight="600"
                  >
                    {item.deliveryDate}
                  </Text>
                  {deliveryStatus === DELIVERY_STATUS.OVERDUE && (
                    <View
                      style={{
                        backgroundColor: `${colors.error}15`,
                        paddingHorizontal: spacing.xs,
                        paddingVertical: 2,
                        borderRadius: borderRadius.small,
                        borderWidth: 1,
                        borderColor: `${colors.error}25`,
                      }}
                    >
                      <Text
                        color={colors.error}
                        fontSize={10}
                        fontFamily="$body"
                        fontWeight="600"
                        textTransform="uppercase"
                        letterSpacing={0.5}
                      >
                        Overdue
                      </Text>
                    </View>
                  )}
                  {deliveryStatus === DELIVERY_STATUS.CURRENT && (
                    <View
                      style={{
                        backgroundColor: `${colors.warning}15`,
                        paddingHorizontal: spacing.xs,
                        paddingVertical: 2,
                        borderRadius: borderRadius.small,
                        borderWidth: 1,
                        borderColor: `${colors.warning}25`,
                      }}
                    >
                      <Text
                        color={colors.warning}
                        fontSize={10}
                        fontFamily="$body"
                        fontWeight="600"
                        textTransform="uppercase"
                        letterSpacing={0.5}
                      >
                        Due Today
                      </Text>
                    </View>
                  )}
                </XStack>
              </YStack>
            </XStack>
          )}

          {/* Measurements Section */}
          {measurementEntries.length > 0 && (
            <YStack gap="$2">
              <Text
                color={colors.text}
                fontSize={14}
                fontFamily="$body"
                fontWeight="600"
                marginBottom="$1"
              >
                Measurements
              </Text>
              <XStack flexWrap="wrap" gap="$2">
                {measurementEntries.slice(0, 6).map(([key, value], idx) => (
                  <View
                    key={idx}
                    style={{
                      backgroundColor: `${cardGradient[0]}12`,
                      paddingHorizontal: spacing.sm,
                      paddingVertical: spacing.xs,
                      borderRadius: borderRadius.medium,
                      borderWidth: 1,
                      borderColor: `${cardGradient[0]}25`,
                      minWidth: 80,
                    }}
                  >
                    <Text
                      color={cardGradient[0]}
                      fontSize={10}
                      fontWeight="500"
                      fontFamily="$body"
                      textTransform="uppercase"
                      letterSpacing={0.5}
                      marginBottom="$1"
                    >
                      {key.charAt(0).toUpperCase() + key.slice(1)}
                    </Text>
                    <Text
                      color={colors.text}
                      fontSize={13}
                      fontWeight="600"
                      fontFamily="$heading"
                    >
                      {value}
                    </Text>
                  </View>
                ))}
                {measurementEntries.length > 6 && (
                  <View
                    style={{
                      backgroundColor: `${colors.textTertiary}10`,
                      paddingHorizontal: spacing.sm,
                      paddingVertical: spacing.xs,
                      borderRadius: borderRadius.medium,
                      borderWidth: 1,
                      borderColor: `${colors.textTertiary}25`,
                      justifyContent: 'center',
                      alignItems: 'center',
                      minWidth: 60,
                    }}
                  >
                    <Text
                      color={colors.textSecondary}
                      fontSize={12}
                      fontWeight="600"
                      fontFamily="$body"
                    >
                      +{measurementEntries.length - 6}
                    </Text>
                  </View>
                )}
              </XStack>
            </YStack>
          )}

          {/* Notes Section */}
          {item.notes && (
            <YStack gap="$2">
              <XStack gap="$2" alignItems="center">
                <View
                  style={{
                    width: 32,
                    height: 32,
                    borderRadius: borderRadius.medium,
                    backgroundColor: `${colors.info}15`,
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: `${colors.info}25`,
                  }}
                >
                  <FileTextIcon size={14} color={colors.info} />
                </View>
                <Text
                  color={colors.text}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="600"
                >
                  Notes
                </Text>
              </XStack>
              <View
                style={{
                  backgroundColor: `${colors.info}08`,
                  padding: spacing.sm,
                  borderRadius: borderRadius.medium,
                  borderWidth: 1,
                  borderColor: `${colors.info}15`,
                }}
              >
                <Text
                  color={colors.textSecondary}
                  numberOfLines={3}
                  ellipsizeMode="tail"
                  fontFamily="$body"
                  fontSize={13}
                  lineHeight={18}
                >
                  {item.notes}
                </Text>
              </View>
            </YStack>
          )}
        </YStack>
      </Card>
    );
  };

  return (
    <YStack f={1} style={{ backgroundColor: colors.background }}>
      {/* Modern Glass Morphism Header */}
      <YStack
        style={{
          background: `linear-gradient(135deg, ${colors.primaryGradient[0]}E6, ${colors.primaryGradient[1]}E6)`,
          backdropFilter: 'blur(20px)',
          borderBottomLeftRadius: borderRadius.extraLarge,
          borderBottomRightRadius: borderRadius.extraLarge,
          ...shadows.large,
        }}
      >
        <GradientWrapper
          colors={[...colors.primaryGradient, colors.secondaryGradient[0]] as const}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{
            paddingHorizontal: spacing.lg,
            paddingTop: spacing.xl + 10,
            paddingBottom: spacing.lg,
            borderBottomLeftRadius: borderRadius.extraLarge,
            borderBottomRightRadius: borderRadius.extraLarge,
          }}
        >
          {/* Compact Header Content */}
          <YStack marginBottom="$3">
            <XStack justifyContent="space-between" alignItems="center" marginBottom="$3">
              <YStack flex={1}>
                <Text
                  color="rgba(255, 255, 255, 0.7)"
                  fontSize={13}
                  fontWeight="500"
                  fontFamily="$body"
                  letterSpacing={0.3}
                  textTransform="uppercase"
                  marginBottom="$1"
                >
                  {TEXT_CONSTANTS.WELCOME_MESSAGE}
                </Text>
                <H1
                  color={colors.textLight}
                  fontSize={28}
                  fontWeight="800"
                  fontFamily="$heading"
                  letterSpacing={-1}
                  lineHeight={32}
                >
                  {TEXT_CONSTANTS.APP_TITLE}
                </H1>
              </YStack>

              {/* Enhanced Total Badge with Progress */}
              <View
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.25)',
                  borderRadius: borderRadius.large,
                  paddingHorizontal: spacing.lg,
                  paddingVertical: spacing.md,
                  borderWidth: 1,
                  borderColor: 'rgba(255, 255, 255, 0.4)',
                  ...shadows.medium,
                  minWidth: 120,
                }}
              >
                <Text
                  color="rgba(255, 255, 255, 0.9)"
                  fontSize={11}
                  fontWeight="600"
                  fontFamily="$body"
                  textAlign="center"
                  marginBottom="$1"
                  textTransform="uppercase"
                  letterSpacing={0.5}
                >
                  Bills Loaded
                </Text>
                <Text
                  color={colors.textLight}
                  fontSize={22}
                  fontWeight="800"
                  fontFamily="$heading"
                  textAlign="center"
                  letterSpacing={-0.5}
                >
                  {totalCount !== null ? `${filteredMeasurements.length}/${totalCount}` : filteredMeasurements.length}
                </Text>
              </View>
            </XStack>
          </YStack>

          {/* Clean Search Section */}
          <YStack gap="$3">
            {/* Modern Search Bar */}
            <XStack
              alignItems="center"
              backgroundColor="white"
              borderRadius={borderRadius.large}
              paddingHorizontal={spacing.md}
              style={{
                height: 48,
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.9)',
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 2,
              }}
            >
              {isSearching ? (
                <ActivityIndicator size="small" color={colors.primary} />
              ) : (
                <SearchIcon size={20} color={colors.textSecondary} />
              )}
              <TextInput
                style={{
                  flex: 1,
                  height: 48,
                  paddingHorizontal: spacing.sm,
                  fontSize: 15,
                  fontWeight: '400',
                  color: colors.text,
                  fontFamily: 'Poppins_400Regular',
                  backgroundColor: 'transparent',
                  borderWidth: 0,
                  textDecorationLine: 'none',
                }}
                placeholder="Search customers, bills, or phone numbers"
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholderTextColor={colors.textTertiary}
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="search"
                editable={!isSearching}
                underlineColorAndroid="transparent"
              />
              {searchQuery.trim() !== '' && (
                <Button
                  size="$2"
                  chromeless
                  circular
                  onPress={() => setSearchQuery('')}
                  style={{
                    width: 28,
                    height: 28,
                    backgroundColor: colors.backgroundAlt,
                    borderRadius: 14,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Text color={colors.textSecondary} fontSize={16} fontWeight="500">×</Text>
                </Button>
              )}
            </XStack>

            {/* Clean Search Results Info */}
            {searchQuery.trim() !== '' && (
              <XStack justifyContent="center" alignItems="center" gap="$2">
                <View
                  style={{
                    width: 6,
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: isSearching ? colors.warning : colors.success,
                  }}
                />
                <Text
                  color="rgba(255, 255, 255, 0.9)"
                  fontSize={13}
                  fontFamily="$body"
                  fontWeight="500"
                  letterSpacing={0.1}
                >
                  {isSearching
                    ? 'Searching...'
                    : `${filteredMeasurements.length} result${filteredMeasurements.length !== 1 ? 's' : ''} found`
                  }
                </Text>
              </XStack>
            )}
          </YStack>
        </GradientWrapper>
      </YStack>



      {loading ? (
        <SkeletonLoader count={6} />
      ) : error ? (
        <YStack
          alignItems="center"
          padding="$8"
          marginTop="$8"
          marginHorizontal={spacing.lg}
          backgroundColor={colors.card}
          borderRadius={borderRadius.card}
          style={{
            ...shadows.medium,
            borderWidth: 1,
            borderColor: `${colors.error}20`,
          }}
        >
          <View
            style={{
              width: 60,
              height: 60,
              borderRadius: 30,
              backgroundColor: `${colors.error}15`,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: spacing.md,
            }}
          >
            <Text fontSize={24}>⚠️</Text>
          </View>
          <Text
            color={colors.text}
            fontSize={18}
            fontWeight="600"
            marginBottom="$2"
            fontFamily="$heading"
          >
            Something went wrong
          </Text>
          <Text
            color={colors.textSecondary}
            textAlign="center"
            marginBottom="$4"
            fontFamily="$body"
            lineHeight={20}
          >
            {error}
          </Text>
          <Button
            marginTop="$4"
            backgroundColor={colors.primary}
            color={colors.textLight}
            borderRadius={borderRadius.button}
            paddingHorizontal={spacing.lg}
            height={48}
            onPress={async () => {
              setLoading(true);
              setError(null);


              // Re-initialize the subscription to load measurements with pagination
              const subscription = subscribeMeasurements(
                (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean, hasMore: boolean }) => {
                  console.log('📱 Retry: Updating UI with measurements:', updatedMeasurements.length);
                  setMeasurements(updatedMeasurements);
                  setLoading(false);
                  setLoadingMore(paginationInfo.isLoading);
                  setAllLoaded(paginationInfo.allLoaded);
                  setError(null);
                },
                20, // Page size
                (error: Error) => {
                  console.error('❌ Retry: Error in measurements subscription:', error);
                  let errorMessage = 'Failed to load measurements. Please try again.';

                  if (error.message.includes('timeout')) {
                    errorMessage = 'Connection timeout. Please check your internet connection and try again.';
                  } else if (error.message.includes('permission')) {
                    errorMessage = 'Permission denied. Please check your database access.';
                  } else if (error.message.includes('network')) {
                    errorMessage = 'Network error. Please check your internet connection.';
                  }

                  setError(errorMessage);
                  setLoading(false);
                  setLoadingMore(false);
                }
              ) as SubscriptionFunctions;

              // Update the ref
              measurementsRef.current = subscription;
            }}
          >
            Try Again
          </Button>
        </YStack>
      ) : (
        <FlatList
          data={filteredMeasurements}
          renderItem={renderMeasurementItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}

          contentContainerStyle={{
            paddingHorizontal: spacing.lg,
            paddingTop: spacing.md,
            paddingBottom: spacing.xxl * 2,
          }}
          style={{
            backgroundColor: colors.background,
          }}

          ListEmptyComponent={
            <YStack
              alignItems="center"
              padding="$6"
              marginTop="$4"
              marginHorizontal={spacing.lg}
            >
              {/* Modern Empty State Illustration */}
              <YStack
                alignItems="center"
                backgroundColor={colors.card}
                borderRadius={borderRadius.extraLarge}
                padding="$8"
                style={{
                  ...shadows.large,
                  borderWidth: 1,
                  borderColor: colors.border,
                  width: '100%',
                }}
              >
                {/* Animated Icon Container */}
                <View
                  style={{
                    width: UI_CONSTANTS.AVATAR_SIZE_EXTRA_LARGE,
                    height: UI_CONSTANTS.AVATAR_SIZE_EXTRA_LARGE,
                    borderRadius: UI_CONSTANTS.BORDER_RADIUS_EXTRA_LARGE,
                    backgroundColor: `${colors.primary}08`,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: spacing.lg,
                    borderWidth: 3,
                    borderColor: `${colors.primary}15`,
                  }}
                >
                  <View
                    style={{
                      width: UI_CONSTANTS.AVATAR_SIZE_EXTRA_LARGE * 0.67, // 80px when parent is 120px
                      height: UI_CONSTANTS.AVATAR_SIZE_EXTRA_LARGE * 0.67,
                      borderRadius: UI_CONSTANTS.BORDER_RADIUS_LARGE,
                      backgroundColor: `${colors.primary}15`,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    <Text fontSize={UI_CONSTANTS.FONT_SIZE_EXTRA_LARGE}>
                      {searchQuery.trim() !== '' ? TEXT_CONSTANTS.EMOJI_SEARCH : TEXT_CONSTANTS.EMOJI_RULER}
                    </Text>
                  </View>
                </View>

                {/* Title and Description */}
                <Text
                  color={colors.text}
                  fontSize={24}
                  fontWeight="700"
                  marginBottom="$3"
                  fontFamily="$heading"
                  textAlign="center"
                  letterSpacing={-0.5}
                >
                  {searchQuery.trim() !== ''
                    ? TEXT_CONSTANTS.NO_RESULTS_TITLE
                    : TEXT_CONSTANTS.READY_TO_START_TITLE
                  }
                </Text>

                <Text
                  color={colors.textSecondary}
                  textAlign="center"
                  fontFamily="$body"
                  fontSize={16}
                  lineHeight={24}
                  marginBottom="$6"
                  maxWidth={280}
                >
                  {searchQuery.trim() !== ''
                    ? `We couldn't find any measurements matching "${searchQuery}". Try adjusting your search terms.`
                    : TEXT_CONSTANTS.READY_TO_START_DESCRIPTION
                  }
                </Text>

                {/* Action Buttons */}
                <YStack gap="$3" width="100%" maxWidth={240}>
                  {searchQuery.trim() !== '' ? (
                    <Button
                      backgroundColor={colors.primary}
                      color={colors.textLight}
                      borderRadius={borderRadius.large}
                      height={52}
                      fontSize={16}
                      fontWeight="600"
                      fontFamily="$body"
                      onPress={() => setSearchQuery('')}
                      style={{
                        ...shadows.button,
                      }}
                    >
                      {TEXT_CONSTANTS.CLEAR_SEARCH_BUTTON}
                    </Button>
                  ) : (
                    <Button
                      backgroundColor={colors.primary}
                      color={colors.textLight}
                      borderRadius={borderRadius.large}
                      height={52}
                      fontSize={16}
                      fontWeight="600"
                      fontFamily="$body"
                      onPress={() => navigation.navigate('AddBill')}
                      style={{
                        ...shadows.button,
                      }}
                    >
                      {TEXT_CONSTANTS.ADD_FIRST_MEASUREMENT_BUTTON}
                    </Button>
                  )}

                  <Button
                    backgroundColor="transparent"
                    color={colors.textSecondary}
                    borderRadius={borderRadius.large}
                    height={48}
                    fontSize={14}
                    fontWeight="500"
                    fontFamily="$body"
                    borderWidth={1}
                    borderColor={colors.border}
                    onPress={() => {
                      // Could add help or tutorial navigation here
                      console.log('Help/Tutorial pressed');
                    }}
                  >
                    {TEXT_CONSTANTS.LEARN_MORE_BUTTON}
                  </Button>
                </YStack>
              </YStack>
            </YStack>
          }
          onEndReached={() => {
            console.log('📱 onEndReached triggered:', { loadingMore, allLoaded, hasRef: !!measurementsRef.current });
            if (!loadingMore && !allLoaded && measurementsRef.current) {
              console.log('🔄 Calling loadMore...');
              measurementsRef.current.loadMore();
            } else {
              console.log('🚫 Not calling loadMore:', { loadingMore, allLoaded, hasRef: !!measurementsRef.current });
            }
          }}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            loadingMore ? (
              <YStack padding="$6" alignItems="center">
                <ActivityIndicator size="small" color={colors.primary} />
                <Text
                  marginTop="$2"
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="500"
                >
                  Loading more measurements...
                </Text>
              </YStack>
            ) : allLoaded && measurements.length > 0 ? (
              <YStack padding="$6" alignItems="center">
                <View
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: `${colors.primary}10`,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: spacing.sm,
                  }}
                >
                  <Text fontSize={18}>✓</Text>
                </View>
                <Text
                  color={colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="500"
                >
                  You've reached the end
                </Text>
              </YStack>
            ) : null
          }
        />
      )}

      <FloatingActionButton
        onPress={() => {
          // Navigate to the new AddBill screen
          navigation.navigate('AddBill');
        }}
      />
    </YStack>
  );
};

export default HomeScreen;
