import React, { useState, useEffect } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import { Button, H1, XStack, YStack, Text, Switch, Separator, Card, Progress, Input } from 'tamagui';
import { Alert, ActivityIndicator, ScrollView, View, TouchableOpacity, Modal, FlatList } from 'react-native';
import { HomeIcon, MoonIcon, BellIcon, GlobeIcon, LockIcon, HelpCircleIcon, TrashIcon, CalendarIcon, ChevronDownIcon, CheckIcon, EyeIcon, EyeOffIcon, SettingsIcon, PhoneIcon } from '../components/TabIcons';
import { deleteMeasurementsByYear, getTotalMeasurementsCount, getAvailableYears } from '../services/measurementService';
import { colors, borderRadius, spacing } from '../theme/colors';
import { TEXT_CONSTANTS, UI_CONSTANTS } from '../constants';

type Props = NativeStackScreenProps<RootStackParamList, 'Settings'>;

const SettingsScreen = ({ navigation }: Props) => {
  const [isDarkMode, setIsDarkMode] = React.useState(false);
  const [isNotificationsEnabled, setIsNotificationsEnabled] = React.useState(true);
  const [isLocationEnabled, setIsLocationEnabled] = React.useState(false);

  // Available years state
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [isLoadingYears, setIsLoadingYears] = useState(true);

  // Year selection dropdown state
  const [selectedYearForDeletion, setSelectedYearForDeletion] = useState<number | null>(null);
  const [isYearDropdownVisible, setIsYearDropdownVisible] = useState(false);

  // Authentication state for delete operations
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAuthModalVisible, setIsAuthModalVisible] = useState(false);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [pendingDeleteYear, setPendingDeleteYear] = useState<number | null>(null);

  const DELETE_PASSWORD = 'aniket_DEL';

  // Bulk delete state
  const [isDeleting, setIsDeleting] = useState(false);
  const [currentDeletingYear, setCurrentDeletingYear] = useState<number | null>(null);
  const [showProgressLog, setShowProgressLog] = useState(false);
  const [deleteProgress, setDeleteProgress] = useState({
    phase: '',
    scannedCount: 0,
    totalCount: 0,
    foundCount: 0,
    deletedCount: 0,
    totalToDelete: 0,
    batchIndex: 0,
    totalBatches: 0,
    startTime: null as number | null,
    estimatedTimeRemaining: null,
    deletionRate: 0,
    errors: [],
    progressLog: []
  });

  // Load available years on component mount
  useEffect(() => {
    const loadAvailableYears = async () => {
      try {
        setIsLoadingYears(true);
        const years = await getAvailableYears();
        setAvailableYears(years);
      } catch (error) {
        console.error('Error loading available years:', error);
        setAvailableYears([]);
      } finally {
        setIsLoadingYears(false);
      }
    };

    loadAvailableYears();
  }, []);

  const [totalCount, setTotalCount] = useState<number | null>(null);

  // Year dropdown functions
  const openYearDropdown = () => {
    if (!isLoadingYears && availableYears.length > 0) {
      setIsYearDropdownVisible(true);
    }
  };

  const closeYearDropdown = () => {
    setIsYearDropdownVisible(false);
  };

  const selectYearForDeletion = (year: number) => {
    setSelectedYearForDeletion(year);
    closeYearDropdown();
    // Check authentication before proceeding
    if (isAuthenticated) {
      handleDeleteRecordsByYear(year);
    } else {
      setPendingDeleteYear(year);
      setIsAuthModalVisible(true);
    }
  };

  // Authentication functions
  const handleAuthentication = () => {
    if (password === DELETE_PASSWORD) {
      setIsAuthenticated(true);
      setIsAuthModalVisible(false);
      setPassword('');

      // Proceed with pending delete operation
      if (pendingDeleteYear) {
        handleDeleteRecordsByYear(pendingDeleteYear);
        setPendingDeleteYear(null);
      }

      Alert.alert(
        'Authentication Successful',
        'You are now authenticated for delete operations.',
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert(
        'Authentication Failed',
        'Incorrect password. Please try again.',
        [{ text: 'OK' }]
      );
      setPassword('');
    }
  };

  const closeAuthModal = () => {
    setIsAuthModalVisible(false);
    setPassword('');
    setPendingDeleteYear(null);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Get total count on component mount
  React.useEffect(() => {
    const fetchTotalCount = async () => {
      try {
        const count = await getTotalMeasurementsCount();
        setTotalCount(count);
        console.log(`📊 Total measurements in database: ${count}`);
      } catch (error) {
        console.error('❌ Error fetching total count:', error);
      }
    };

    fetchTotalCount();
  }, []);

  const handleDeleteRecordsByYear = (year: number) => {
    Alert.alert(
      `Delete ${year} Records`,
      `Are you sure you want to delete ALL measurements with delivery date in ${year}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: `Delete All ${year} Records`,
          style: 'destructive',
          onPress: () => confirmDeleteRecordsByYear(year)
        }
      ]
    );
  };

  const confirmDeleteRecordsByYear = async (year: number) => {
    try {
      setIsDeleting(true);
      setCurrentDeletingYear(year);
      const startTime = Date.now();

      setDeleteProgress({
        phase: 'starting',
        scannedCount: 0,
        totalCount: 0,
        foundCount: 0,
        deletedCount: 0,
        totalToDelete: 0,
        batchIndex: 0,
        totalBatches: 0,
        startTime,
        estimatedTimeRemaining: null,
        deletionRate: 0,
        errors: [],
        progressLog: []
      });

      const result = await deleteMeasurementsByYear(year, (progress: any) => {
        const currentTime = Date.now();
        const elapsedTime = currentTime - startTime;

        // Calculate deletion rate and estimated time remaining
        let estimatedTimeRemaining = null;
        let deletionRate = 0;

        if (progress.phase === 'deleting' && progress.deletedCount > 0) {
          deletionRate = progress.deletedCount / (elapsedTime / 1000); // records per second
          const remainingRecords = progress.totalToDelete - progress.deletedCount;
          estimatedTimeRemaining = remainingRecords / deletionRate; // seconds
        }

        // Create progress log entry
        const logEntry = {
          timestamp: currentTime,
          phase: progress.phase,
          message: progress.phase === 'scanning'
            ? `Scanned ${progress.scannedCount}/${progress.totalCount} records (found ${progress.foundCount} from ${year})`
            : progress.phase === 'deleting'
            ? `Deleted ${progress.deletedCount}/${progress.totalToDelete} records (batch ${progress.batchIndex}/${progress.totalBatches})`
            : 'Starting deletion process...',
          details: progress
        };

        setDeleteProgress(prev => ({
          ...progress,
          startTime,
          estimatedTimeRemaining,
          deletionRate,
          errors: result?.errors || [],
          progressLog: [...prev.progressLog, logEntry].slice(-20) // Keep last 20 entries
        }));
      });

      setIsDeleting(false);
      setCurrentDeletingYear(null);

      // Refresh total count after deletion
      try {
        const updatedCount = await getTotalMeasurementsCount();
        setTotalCount(updatedCount);
        console.log(`📊 Updated total count after deletion: ${updatedCount}`);
      } catch (error) {
        console.error('❌ Error refreshing total count after deletion:', error);
      }

      // Enhanced result reporting
      const totalTime = (Date.now() - startTime) / 1000; // seconds
      const avgRate = result.deletedCount / totalTime;

      if (result.errors.length > 0) {
        Alert.alert(
          'Deletion Completed with Errors',
          `Successfully deleted ${result.deletedCount} records in ${totalTime.toFixed(1)}s (${avgRate.toFixed(1)} records/sec).\n\nEncountered ${result.errors.length} errors. Some records may not have been deleted.`,
          [{ text: 'View Details', onPress: () => showErrorDetails(result.errors) }, { text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Deletion Successful',
          `Successfully deleted ${result.deletedCount} measurements with delivery date in ${year}.\n\nCompleted in ${totalTime.toFixed(1)} seconds (${avgRate.toFixed(1)} records/sec).`,
          [{ text: 'OK' }]
        );
      }

    } catch (error: any) {
      setIsDeleting(false);
      setCurrentDeletingYear(null);
      console.error(`Error deleting ${year} records:`, error);
      Alert.alert(
        'Deletion Failed',
        `Failed to delete ${year} records: ${error?.message}\n\nPlease try again.`,
        [{ text: 'OK' }]
      );
    }
  };

  const showErrorDetails = (errors: any[]) => {
    const errorSummary = errors.map((err, index) =>
      `Batch ${err.batchIndex}: ${err.error} (${err.documentsInBatch} docs)`
    ).join('\n');

    Alert.alert(
      'Error Details',
      `The following errors occurred during deletion:\n\n${errorSummary}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <YStack f={1} style={{ backgroundColor: colors.background }}>
      {/* Modern Professional Header */}
      <YStack
        style={{
          backgroundColor: colors.primary,
          borderBottomLeftRadius: borderRadius.extraLarge,
          borderBottomRightRadius: borderRadius.extraLarge,
        }}
      >
       
      </YStack>

      {/* Content */}
      <ScrollView style={{ flex: 1 }}>
        <YStack paddingHorizontal={spacing.lg} paddingTop={spacing.lg} gap="$5">

         

          {/* Data Management Section */}
          <YStack gap="$3">
            <Text
              color={colors.text}
              fontSize={20}
              fontWeight="800"
              fontFamily="$heading"
              letterSpacing={-0.6}
              marginBottom="$2"
            >
              Data Management
            </Text>

            <Card
              padding={spacing.lg}
              backgroundColor={colors.card}
              borderColor={colors.border}
              borderWidth={1}
              borderRadius={borderRadius.card}
            >
              <YStack gap="$4">
                {/* Database Statistics */}
                {totalCount !== null && (
                  <View
                    style={{
                      backgroundColor: `${colors.primary}08`,
                      borderRadius: borderRadius.medium,
                      padding: spacing.lg,
                      borderWidth: 1,
                      borderColor: `${colors.primary}20`,
                    }}
                  >
                    <XStack justifyContent="space-between" alignItems="center">
                      <XStack alignItems="center" gap="$3">
                        <View
                          style={{
                            width: 40,
                            height: 40,
                            borderRadius: 20,
                            backgroundColor: `${colors.primary}20`,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          <Text fontSize={20}>📊</Text>
                        </View>
                        <YStack>
                          <Text
                            fontSize={16}
                            fontWeight="700"
                            color={colors.text}
                            fontFamily="$heading"
                            letterSpacing={-0.3}
                          >
                            Database Statistics
                          </Text>
                          <Text
                            fontSize={13}
                            color={colors.textSecondary}
                            fontFamily="$body"
                            fontWeight="500"
                          >
                            Total bills in database
                          </Text>
                        </YStack>
                      </XStack>
                      <YStack alignItems="flex-end">
                        <Text
                          fontSize={24}
                          fontWeight="800"
                          color={colors.primary}
                          fontFamily="$heading"
                          letterSpacing={-0.8}
                        >
                          {totalCount.toLocaleString()}
                        </Text>
                        <Text
                          fontSize={12}
                          color={colors.textSecondary}
                          fontFamily="$body"
                          fontWeight="500"
                        >
                          bills stored
                        </Text>
                      </YStack>
                    </XStack>
                  </View>
                )}

                <Separator borderColor={colors.border} />

                {/* Delete Records by Year */}
                <YStack gap="$4">
                  <XStack alignItems="center" justifyContent="space-between">
                    <XStack alignItems="center" gap="$3">
                      <View
                        style={{
                          width: 40,
                          height: 40,
                          borderRadius: 20,
                          backgroundColor: `${colors.error}15`,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <TrashIcon size={20} color={colors.error} />
                      </View>
                      <YStack>
                        <Text
                          fontSize={16}
                          fontWeight="700"
                          color={colors.text}
                          fontFamily="$heading"
                          letterSpacing={-0.3}
                        >
                          Delete Records by Year
                        </Text>
                        <Text
                          fontSize={13}
                          color={colors.textSecondary}
                          fontFamily="$body"
                          fontWeight="500"
                        >
                          Remove data by delivery year
                        </Text>
                      </YStack>
                    </XStack>

                    {/* Authentication Status */}
                    <View
                      style={{
                        backgroundColor: isAuthenticated ? `${colors.success}15` : `${colors.textSecondary}10`,
                        borderRadius: borderRadius.tag,
                        paddingHorizontal: spacing.sm,
                        paddingVertical: spacing.xs,
                        borderWidth: 1,
                        borderColor: isAuthenticated ? `${colors.success}30` : `${colors.textSecondary}20`,
                      }}
                    >
                      <XStack alignItems="center" gap="$2">
                        <LockIcon
                          size={12}
                          color={isAuthenticated ? colors.success : colors.textSecondary}
                        />
                        <Text
                          fontSize={11}
                          color={isAuthenticated ? colors.success : colors.textSecondary}
                          fontWeight="600"
                          fontFamily="$body"
                          textTransform="uppercase"
                          letterSpacing={0.5}
                        >
                          {isAuthenticated ? 'Authenticated' : 'Not Auth'}
                        </Text>
                      </XStack>
                    </View>
                  </XStack>

                  {/* Loading state */}
                  {isLoadingYears ? (
                    <YStack gap="$3" alignItems="center" padding="$4">
                      <ActivityIndicator size="small" color={colors.primary} />
                      <Text fontSize={14} color={colors.textSecondary} fontFamily="$body">
                        Loading available years...
                      </Text>
                    </YStack>
                  ) : availableYears.length === 0 ? (
                    <YStack gap="$3" alignItems="center" padding="$4">
                      <Text fontSize={14} color={colors.textSecondary} textAlign="center" fontFamily="$body">
                        No data available for deletion.
                      </Text>
                    </YStack>
                  ) : (
                    /* Year selection dropdown */
                    <YStack gap="$3">
                      {!isAuthenticated && (
                        <View
                          style={{
                            backgroundColor: `${colors.warning}08`,
                            borderRadius: borderRadius.small,
                            padding: spacing.md,
                            borderWidth: 1,
                            borderColor: `${colors.warning}20`,
                          }}
                        >
                          <Text
                            fontSize={12}
                            color={colors.warning}
                            fontFamily="$body"
                            fontWeight="600"
                            textAlign="center"
                          >
                            ⚠️ Authentication required for delete operations
                          </Text>
                        </View>
                      )}

                      {/* Year Dropdown Button */}
                      <TouchableOpacity
                        onPress={openYearDropdown}
                        disabled={isDeleting}
                        style={{
                          backgroundColor: colors.backgroundAlt,
                          borderColor: colors.border,
                          borderWidth: 1,
                          borderRadius: borderRadius.input,
                          paddingHorizontal: spacing.lg,
                          paddingVertical: spacing.md,
                          minHeight: 56,
                          opacity: isDeleting ? 0.6 : 1,
                        }}
                        activeOpacity={0.7}
                      >
                        <XStack alignItems="center" justifyContent="space-between">
                          <XStack alignItems="center" gap="$3">
                            <CalendarIcon size={20} color={colors.textSecondary} />
                            <Text
                              fontSize={16}
                              color={selectedYearForDeletion ? colors.text : colors.textSecondary}
                              fontFamily="$body"
                              fontWeight="500"
                            >
                              {selectedYearForDeletion ? `${selectedYearForDeletion}` : 'Select year to delete'}
                            </Text>
                          </XStack>
                          <ChevronDownIcon size={16} color={colors.textSecondary} />
                        </XStack>
                      </TouchableOpacity>
                    </YStack>
                  )}
                </YStack>

                {/* Progress Display - shown when any deletion is in progress */}
                {isDeleting && (
                  <YStack gap="$3" padding="$4" backgroundColor={colors.backgroundAlt} borderRadius={borderRadius.medium} borderWidth={1} borderColor={colors.border}>
                {/* Header with phase and spinner */}
                <XStack justifyContent="space-between" alignItems="center">
                  <YStack flex={1}>
                    <Text fontSize={16} fontWeight="600" color={colors.text}>
                      {deleteProgress.phase === 'scanning' && `🔍 Scanning ${currentDeletingYear} Records`}
                      {deleteProgress.phase === 'deleting' && `🗑️ Deleting ${currentDeletingYear} Records`}
                      {deleteProgress.phase === 'starting' && `⚡ Initializing ${currentDeletingYear} Deletion`}
                    </Text>
                    {deleteProgress.startTime && (
                      <Text fontSize={12} color={colors.textSecondary}>
                        Started: {new Date(deleteProgress.startTime).toLocaleTimeString()}
                      </Text>
                    )}
                  </YStack>
                  <ActivityIndicator size="small" color={colors.primary} />
                </XStack>

                {/* Scanning Phase Progress */}
                {deleteProgress.phase === 'scanning' && (
                  <YStack space="$2">
                    <XStack justifyContent="space-between">
                      <Text fontSize={13} color={colors.text} fontWeight="500">
                        Scanning Progress
                      </Text>
                      <Text fontSize={13} color={colors.textSecondary}>
                        {deleteProgress.scannedCount}/{deleteProgress.totalCount}
                      </Text>
                    </XStack>

                    <Progress
                      value={deleteProgress.totalCount > 0 ? (deleteProgress.scannedCount / deleteProgress.totalCount) * 100 : 0}
                      backgroundColor={colors.border}
                      style={{ height: 6 }}
                    >
                      <Progress.Indicator backgroundColor={colors.primary} />
                    </Progress>

                    <XStack justifyContent="space-between">
                      <Text fontSize={12} color={colors.textSecondary}>
                        Found {currentDeletingYear} records: {deleteProgress.foundCount}
                      </Text>
                      <Text fontSize={12} color={colors.textSecondary}>
                        {deleteProgress.totalCount > 0 ? Math.round((deleteProgress.scannedCount / deleteProgress.totalCount) * 100) : 0}%
                      </Text>
                    </XStack>
                  </YStack>
                )}

                {/* Deletion Phase Progress */}
                {deleteProgress.phase === 'deleting' && (
                  <YStack space="$2">
                    <XStack justifyContent="space-between">
                      <Text fontSize={13} color={colors.text} fontWeight="500">
                        Deletion Progress
                      </Text>
                      <Text fontSize={13} color={colors.textSecondary}>
                        {deleteProgress.deletedCount}/{deleteProgress.totalToDelete}
                      </Text>
                    </XStack>

                    <Progress
                      value={deleteProgress.totalToDelete > 0 ? (deleteProgress.deletedCount / deleteProgress.totalToDelete) * 100 : 0}
                      backgroundColor={colors.border}
                      style={{ height: 8 }}
                    >
                      <Progress.Indicator backgroundColor={colors.error} />
                    </Progress>

                    <XStack justifyContent="space-between">
                      <Text fontSize={12} color={colors.textSecondary}>
                        Batch: {deleteProgress.batchIndex}/{deleteProgress.totalBatches}
                      </Text>
                      <Text fontSize={12} color={colors.textSecondary}>
                        {deleteProgress.totalToDelete > 0 ? Math.round((deleteProgress.deletedCount / deleteProgress.totalToDelete) * 100) : 0}%
                      </Text>
                    </XStack>

                    {/* Performance metrics */}
                    {deleteProgress.deletionRate > 0 && (
                      <XStack justifyContent="space-between">
                        <Text fontSize={11} color={colors.textTertiary}>
                          Rate: {deleteProgress.deletionRate.toFixed(1)} records/sec
                        </Text>
                        {deleteProgress.estimatedTimeRemaining && (
                          <Text fontSize={11} color={colors.textTertiary}>
                            ETA: {deleteProgress.estimatedTimeRemaining < 60
                              ? `${Math.round(deleteProgress.estimatedTimeRemaining)}s`
                              : `${Math.round(deleteProgress.estimatedTimeRemaining / 60)}m`}
                          </Text>
                        )}
                      </XStack>
                    )}
                  </YStack>
                )}

                {/* Error indicator */}
                {deleteProgress.errors && deleteProgress.errors.length > 0 && (
                  <XStack space="$2" alignItems="center" padding="$2" backgroundColor={`${colors.error}10`} borderRadius={borderRadius.small}>
                    <Text fontSize={12} color={colors.error}>
                      ⚠️ {deleteProgress.errors.length} error(s) encountered
                    </Text>
                  </XStack>
                )}

                {/* Progress log toggle */}
                {deleteProgress.progressLog.length > 0 && (
                  <Button
                    size="$2"
                    variant="outlined"
                    onPress={() => setShowProgressLog(!showProgressLog)}
                    style={{
                      borderColor: colors.border,
                      backgroundColor: 'transparent'
                    }}
                  >
                    <Text fontSize={12} color={colors.textSecondary}>
                      {showProgressLog ? 'Hide' : 'Show'} Progress Log ({deleteProgress.progressLog.length})
                    </Text>
                  </Button>
                )}

                {/* Detailed progress log */}
                {showProgressLog && deleteProgress.progressLog.length > 0 && (
                  <YStack space="$1" maxHeight={200} backgroundColor={`${colors.textTertiary}05`} borderRadius={borderRadius.small} padding="$2">
                    <Text fontSize={11} fontWeight="600" color={colors.textSecondary} marginBottom="$1">
                      Progress Log:
                    </Text>
                    <ScrollView style={{ maxHeight: 150 }}>
                      {deleteProgress.progressLog.slice().reverse().map((entry:any, index) => (
                        <YStack key={index} marginBottom="$1">
                          <XStack justifyContent="space-between" alignItems="flex-start">
                            <Text fontSize={10} color={colors.textTertiary} flex={1}>
                              {entry.message}
                            </Text>
                            <Text fontSize={9} color={colors.textTertiary} marginLeft="$2">
                              {new Date(entry.timestamp).toLocaleTimeString()}
                            </Text>
                          </XStack>
                        </YStack>
                      ))}
                    </ScrollView>
                  </YStack>
                  )}
                </YStack>
              )}
              </YStack>
            </Card>
          </YStack>


          {/* Navigation Button */}
          <YStack paddingBottom={spacing.xl} paddingTop="$2">
            <Button
              size="$5"
              backgroundColor={colors.primary}
              color={colors.card}
              onPress={() => navigation.navigate('Home')}
              borderRadius={borderRadius.button}
              fontWeight="700"
              fontSize={17}
              fontFamily="$heading"
              letterSpacing={-0.4}
              icon={HomeIcon}
              pressStyle={{
                backgroundColor: colors.primaryDark,
                transform: [{ scale: 0.98 }]
              }}
            >
              Back to Home
            </Button>
          </YStack>
        </YStack>
      </ScrollView>

      {/* Authentication Modal */}
      <Modal
        visible={isAuthModalVisible}
        transparent
        animationType="slide"
        onRequestClose={closeAuthModal}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: spacing.lg,
          }}
          activeOpacity={1}
          onPress={closeAuthModal}
        >
          <TouchableOpacity
            activeOpacity={1}
            style={{
              backgroundColor: colors.card,
              borderRadius: borderRadius.card,
              width: '100%',
              maxWidth: 350,
              borderWidth: 1,
              borderColor: colors.border,
            }}
            onPress={(e) => e.stopPropagation()}
          >
            <YStack gap="$4" padding="$4">
              {/* Header */}
              <XStack alignItems="center" justifyContent="space-between">
                <XStack alignItems="center" gap="$2">
                  <LockIcon size={24} color={colors.error} />
                  <YStack>
                    <Text fontSize={18} fontWeight="700" color={colors.text}>
                      Authentication Required
                    </Text>
                    <Text fontSize={14} color={colors.textSecondary}>
                      Enter password to delete records
                    </Text>
                  </YStack>
                </XStack>
                <Button
                  size="$3"
                  circular
                  backgroundColor="transparent"
                  color={colors.textSecondary}
                  onPress={closeAuthModal}
                >
                  ✕
                </Button>
              </XStack>

              {/* Warning */}
              <YStack gap="$2" padding="$3" backgroundColor={`${colors.error}10`} borderRadius={borderRadius.small}>
                <XStack alignItems="center" gap="$2">
                  <TrashIcon size={16} color={colors.error} />
                  <Text fontSize={14} fontWeight="600" color={colors.error}>
                    Destructive Operation
                  </Text>
                </XStack>
                <Text fontSize={12} color={colors.textSecondary}>
                  {pendingDeleteYear ? `You are about to delete ALL measurements from ${pendingDeleteYear}. This action cannot be undone.` : 'This will permanently delete data from the database.'}
                </Text>
              </YStack>

              {/* Password Input */}
              <YStack gap="$2">
                <Text fontSize={14} fontWeight="600" color={colors.text}>
                  Password *
                </Text>
                <XStack
                  alignItems="center"
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderWidth={1}
                  borderRadius={borderRadius.input}
                  paddingHorizontal={spacing.md}
                  paddingVertical={spacing.sm}
                >
                  <Input
                    flex={1}
                    placeholder="Enter delete password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={!showPassword}
                    backgroundColor="transparent"
                    borderWidth={0}
                    borderBottomWidth={0}
                    fontSize={16}
                    color={colors.text}
                    placeholderTextColor={colors.textSecondary}
                    onSubmitEditing={handleAuthentication}
                    autoFocus={true}
                    style={{
                      borderBottomWidth: 0,
                      borderBottomColor: 'transparent',
                    }}
                  />
                  <TouchableOpacity
                    onPress={togglePasswordVisibility}
                    style={{ padding: 4 }}
                  >
                    {showPassword ? (
                      <EyeOffIcon size={20} color={colors.textSecondary} />
                    ) : (
                      <EyeIcon size={20} color={colors.textSecondary} />
                    )}
                  </TouchableOpacity>
                </XStack>
              </YStack>

              {/* Buttons */}
              <XStack gap="$3">
                <Button
                  flex={1}
                  size="$4"
                  backgroundColor={colors.backgroundAlt}
                  borderColor={colors.border}
                  borderWidth={1}
                  onPress={closeAuthModal}
                >
                  <Text color={colors.textSecondary}>Cancel</Text>
                </Button>
                <Button
                  flex={1}
                  size="$4"
                  backgroundColor={colors.error}
                  onPress={handleAuthentication}
                  disabled={!password.trim()}
                  opacity={!password.trim() ? 0.6 : 1}
                >
                  <XStack alignItems="center" gap="$2">
                    <LockIcon size={16} color="white" />
                    <Text color="white" fontWeight="600">Authenticate</Text>
                  </XStack>
                </Button>
              </XStack>
            </YStack>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>

      {/* Year Selection Modal */}
      <Modal
        visible={isYearDropdownVisible}
        transparent
        animationType="fade"
        onRequestClose={closeYearDropdown}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: spacing.lg,
          }}
          activeOpacity={1}
          onPress={closeYearDropdown}
        >
          <TouchableOpacity
            activeOpacity={1}
            style={{
              backgroundColor: colors.card,
              borderRadius: borderRadius.card,
              width: '100%',
              maxWidth: 300,
              maxHeight: 400,
              borderWidth: 1,
              borderColor: colors.border,
            }}
            onPress={(e) => e.stopPropagation()}
          >
            <YStack>
              {/* Header */}
              <XStack
                alignItems="center"
                justifyContent="space-between"
                paddingHorizontal={spacing.lg}
                paddingVertical={spacing.md}
                borderBottomWidth={1}
                borderBottomColor={colors.border}
              >
                <Text
                  fontSize={18}
                  fontFamily="$heading"
                  fontWeight="600"
                  color={colors.text}
                >
                  Select Year to Delete
                </Text>
                <Button
                  size="$3"
                  circular
                  backgroundColor="transparent"
                  color={colors.textSecondary}
                  onPress={closeYearDropdown}
                >
                  ✕
                </Button>
              </XStack>

              {/* Year List */}
              <FlatList
                data={availableYears.sort((a, b) => b - a)}
                renderItem={({ item: year }) => (
                  <TouchableOpacity
                    onPress={() => selectYearForDeletion(year)}
                    style={{
                      paddingHorizontal: spacing.lg,
                      paddingVertical: spacing.md,
                      backgroundColor: selectedYearForDeletion === year ? `${colors.error}15` : 'transparent',
                      borderBottomWidth: 1,
                      borderBottomColor: colors.border,
                    }}
                    activeOpacity={0.7}
                  >
                    <XStack alignItems="center" justifyContent="space-between">
                      <XStack alignItems="center" gap="$2">
                        <CalendarIcon size={16} color={colors.textSecondary} />
                        <Text
                          fontSize={16}
                          fontFamily="$body"
                          fontWeight={selectedYearForDeletion === year ? "600" : "400"}
                          color={selectedYearForDeletion === year ? colors.error : colors.text}
                        >
                          {year}
                        </Text>
                      </XStack>
                      {selectedYearForDeletion === year && (
                        <CheckIcon size={20} color={colors.error} />
                      )}
                    </XStack>
                  </TouchableOpacity>
                )}
                keyExtractor={(year) => year.toString()}
                showsVerticalScrollIndicator={false}
                style={{
                  maxHeight: 300,
                }}
              />
            </YStack>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </YStack>
  );
};

export default SettingsScreen;
