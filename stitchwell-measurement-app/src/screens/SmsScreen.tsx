import React, { useState, useEffect, useRef } from 'react';
import { FlatList, Alert, TouchableOpacity, ActivityIndicator, View, TextInput } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import {
  Button,
  H1,
  XStack,
  YStack,
  Card,
  Text,
  ScrollView,
} from 'tamagui';
import {
  UserIcon,
  PhoneIcon,
  SendIcon,
  CheckIcon,
} from '../components/TabIcons';
import { colors, borderRadius, spacing } from '../theme/colors';
import { TEXT_CONSTANTS, UI_CONSTANTS } from '../constants';
import { Measurement } from '../types';
import { subscribeMeasurements } from '../services/measurementService';
import {
  SmsRecipient,
  SmsTemplate,
  SMS_TEMPLATES,
  getUniqueCustomers,
  sendBulkSms,
  confirmSendSms,
  replaceTemplateVariables,
} from '../services/smsService';

type Props = NativeStackScreenProps<RootStackParamList, 'SMS'>;

const SmsScreen = ({ navigation }: Props) => {
  const [measurements, setMeasurements] = useState<Measurement[]>([]);
  const [customers, setCustomers] = useState<SmsRecipient[]>([]);
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [message, setMessage] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<SmsTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [allLoaded, setAllLoaded] = useState(false);
  const [sending, setSending] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Ref to store subscription functions
  const measurementsRef = useRef<{ unsubscribe: Function; loadMore: Function } | null>(null);



  // Subscribe to measurements with batch loading on component mount
  useEffect(() => {
    let isSubscribed = true; // Flag to prevent state updates after unmount

    const initializeData = async () => {
      // Prevent multiple initializations
      if (measurementsRef.current) {
        console.log('🚫 SMS: Subscription already exists, skipping initialization');
        return;
      }

      setLoading(true);
      setError(null);

      console.log('🔄 SMS: Initializing measurements data with batch loading...');

      try {
        // Set up listener to load measurements with pagination
        const subscription = subscribeMeasurements(
          (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean, hasMore: boolean }) => {
            // Only update state if component is still mounted
            if (!isSubscribed) {
              console.log('🚫 SMS: Component unmounted, ignoring subscription update');
              return;
            }

            console.log(`📱 SMS: Updating with measurements: ${updatedMeasurements.length} loaded`);
            setMeasurements(updatedMeasurements);

            // Convert measurements to unique customers
            const uniqueCustomers = getUniqueCustomers(updatedMeasurements);
            console.log(`👥 SMS: Found ${uniqueCustomers.length} unique customers`);
            setCustomers(uniqueCustomers);

            setLoading(false);
            setLoadingMore(paginationInfo.isLoading);
            setAllLoaded(paginationInfo.allLoaded);
            setError(null);
          },
          50, // Larger page size for SMS since we need more data for customer selection
          (error: Error) => {
            // Only update state if component is still mounted
            if (!isSubscribed) {
              console.log('🚫 SMS: Component unmounted, ignoring subscription error');
              return;
            }

            console.error('❌ SMS: Error in measurements subscription:', error);
            let errorMessage = 'Failed to load customers. Please try again.';

            if (error.message.includes('timeout')) {
              errorMessage = 'Connection timeout. Please check your internet connection and try again.';
            } else if (error.message.includes('permission')) {
              errorMessage = 'Permission denied. Please check your database access.';
            } else if (error.message.includes('network')) {
              errorMessage = 'Network error. Please check your internet connection.';
            }

            setError(errorMessage);
            setLoading(false);
            setLoadingMore(false);
            setCustomers([]);
          }
        );

        // Store subscription reference
        measurementsRef.current = subscription;

      } catch (error) {
        console.error('❌ SMS: Error setting up measurements subscription:', error);
        if (isSubscribed) {
          setError('Failed to initialize customer data. Please try again.');
          setLoading(false);
          setCustomers([]);
        }
      }
    };

    initializeData();

    // Cleanup function
    return () => {
      console.log('🧹 SMS: Cleaning up measurements subscription...');
      isSubscribed = false;
      if (measurementsRef.current) {
        measurementsRef.current.unsubscribe();
        measurementsRef.current = null;
      }
    };
  }, []);

  // Sync message when template changes
  useEffect(() => {
    if (selectedTemplate) {
      setMessage(selectedTemplate.message);
    }
  }, [selectedTemplate]);

  // Filter customers based on search query
  const filteredCustomers = (customers || []).filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.phoneNo.includes(searchQuery) ||
    (customer.billNo && customer.billNo.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Toggle customer selection
  const toggleCustomerSelection = (customerId: string) => {
    if (selectedCustomers.includes(customerId)) {
      setSelectedCustomers(selectedCustomers.filter(id => id !== customerId));
    } else {
      setSelectedCustomers([...selectedCustomers, customerId]);
    }
  };

  // Select all customers
  const selectAllCustomers = () => {
    if (selectedCustomers.length === filteredCustomers.length) {
      setSelectedCustomers([]);
    } else {
      const allIds = (filteredCustomers || []).map(c => c.id);
      setSelectedCustomers(allIds);
    }
  };

  // Apply template with simplified logic
  const applyTemplate = (template: SmsTemplate) => {
    // Simply set the template - useEffect will handle message sync
    setSelectedTemplate(template);
    setMessage(template.message);
  };

  // Clear template and message
  const clearTemplate = () => {
    setSelectedTemplate(null);
    setMessage('');
  };

  // Send SMS
  const handleSendSms = async () => {
    if (selectedCustomers.length === 0) {
      Alert.alert('Error', TEXT_CONSTANTS.SMS_NO_CUSTOMERS_SELECTED);
      return;
    }

    if (!message.trim()) {
      Alert.alert('Error', TEXT_CONSTANTS.SMS_NO_MESSAGE);
      return;
    }

    const recipients = (customers || []).filter(c => selectedCustomers.includes(c.id));
    
    confirmSendSms(recipients, message, async () => {
      try {
        setSending(true);
        
        const results = await sendBulkSms(
          recipients,
          message,
          (current, total, recipient) => {
            console.log(`Sending SMS ${current}/${total} to ${recipient.name}`);
          }
        );

        if (results.success > 0) {
          Alert.alert(
            'SMS Sent',
            `Successfully sent ${results.success} SMS${results.success > 1 ? 'es' : ''}` +
            (results.failed > 0 ? `\n${results.failed} failed to send` : ''),
            [{ text: 'OK', onPress: () => {
              setSelectedCustomers([]);
              setMessage('');
              setSelectedTemplate(null);
            }}]
          );
        } else {
          Alert.alert('Error', 'Failed to send any SMS messages');
        }
      } catch (error) {
        console.error('Error sending SMS:', error);
        Alert.alert('Error', TEXT_CONSTANTS.SMS_ERROR);
      } finally {
        setSending(false);
      }
    });
  };

  // Render customer item
  const renderCustomerItem = ({ item }: { item: SmsRecipient }) => {
    const isSelected = selectedCustomers.includes(item.id);

    return (
      <Card
        padding={spacing.lg}
        backgroundColor={isSelected ? `${colors.primary}12` : colors.card}
        borderColor={isSelected ? colors.primary : colors.border}
        borderWidth={isSelected ? 2 : 1}
        borderRadius={borderRadius.card}
        onPress={() => toggleCustomerSelection(item.id)}
        pressStyle={{
          backgroundColor: isSelected ? `${colors.primary}18` : colors.backgroundAlt,
          transform: [{ scale: 0.98 }]
        }}
        style={{
          marginBottom: spacing.sm,
        }}
      >
          <XStack alignItems="center" gap="$4">
            {/* Customer Avatar with Selection Indicator */}
            <View
              style={{
                position: 'relative',
              }}
            >
              <View
                style={{
                  width: UI_CONSTANTS.AVATAR_SIZE_LARGE,
                  height: UI_CONSTANTS.AVATAR_SIZE_LARGE,
                  borderRadius: UI_CONSTANTS.AVATAR_SIZE_LARGE / 2,
                  backgroundColor: isSelected ? `${colors.primary}20` : `${colors.primary}10`,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderWidth: isSelected ? 2 : 0,
                  borderColor: colors.primary,
                }}
              >
                <UserIcon
                  size={UI_CONSTANTS.ICON_SIZE_LARGE}
                  color={isSelected ? colors.primary : colors.textSecondary}
                />
              </View>

              {/* Selection Indicator */}
              {isSelected && (
                <View
                  style={{
                    position: 'absolute',
                    top: -2,
                    right: -2,
                    width: 20,
                    height: 20,
                    borderRadius: 10,
                    backgroundColor: colors.primary,
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: 2,
                    borderColor: colors.card,
                  }}
                >
                  <CheckIcon size={10} color={colors.card} />
                </View>
              )}
            </View>

            {/* Customer Info */}
            <YStack flex={1}>
              <Text
                color={isSelected ? colors.primary : colors.text}
                fontWeight="700"
                fontSize={17}
                fontFamily="$heading"
                numberOfLines={1}
                letterSpacing={-0.4}
                marginBottom="$1"
              >
                {item.name}
              </Text>
              <XStack alignItems="center" gap="$2">
                <PhoneIcon
                  size={UI_CONSTANTS.ICON_SIZE_SMALL}
                  color={isSelected ? colors.primary : colors.textSecondary}
                />
                <Text
                  color={isSelected ? colors.primary : colors.textSecondary}
                  fontSize={14}
                  fontFamily="$body"
                  fontWeight="500"
                >
                  {item.phoneNo}
                </Text>
                {item.billNo && (
                  <>
                    <Text color={colors.textTertiary} fontSize={12}>•</Text>
                    <Text
                      color={colors.textTertiary}
                      fontSize={12}
                      fontFamily="$body"
                      fontWeight="500"
                    >
                      Bill #{item.billNo}
                    </Text>
                  </>
                )}
              </XStack>
            </YStack>

            {/* Selection Status */}
            <View
              style={{
                width: 28,
                height: 28,
                borderRadius: 14,
                backgroundColor: isSelected ? colors.primary : 'transparent',
                borderWidth: 2,
                borderColor: isSelected ? colors.primary : colors.border,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {isSelected && <CheckIcon size={14} color={colors.card} />}
            </View>
          </XStack>
        </Card>
    );
  };

  // Handle retry
  const handleRetry = async () => {
    console.log('🔄 SMS: Retrying data load...');

    // Clean up existing subscription
    if (measurementsRef.current) {
      measurementsRef.current.unsubscribe();
      measurementsRef.current = null;
    }

    // Reset state
    setError(null);
    setLoading(true);
    setMeasurements([]);
    setCustomers([]);
    setSelectedCustomers([]);
    setLoadingMore(false);
    setAllLoaded(false);

    // Re-initialize subscription manually
    try {
      const subscription = subscribeMeasurements(
        (updatedMeasurements: Measurement[], paginationInfo: { isLoading: boolean, allLoaded: boolean, hasMore: boolean }) => {
          console.log(`📱 SMS Retry: Updating with measurements: ${updatedMeasurements.length} loaded`);
          setMeasurements(updatedMeasurements);

          // Convert measurements to unique customers
          const uniqueCustomers = getUniqueCustomers(updatedMeasurements);
          console.log(`👥 SMS Retry: Found ${uniqueCustomers.length} unique customers`);
          setCustomers(uniqueCustomers);

          setLoading(false);
          setLoadingMore(paginationInfo.isLoading);
          setAllLoaded(paginationInfo.allLoaded);
          setError(null);
        },
        50, // Larger page size for SMS
        (error: Error) => {
          console.error('❌ SMS Retry: Error in measurements subscription:', error);
          setError('Failed to load customers. Please try again.');
          setLoading(false);
          setLoadingMore(false);
          setCustomers([]);
        }
      );

      measurementsRef.current = subscription;
    } catch (error) {
      console.error('❌ SMS Retry: Error setting up subscription:', error);
      setError('Failed to initialize customer data. Please try again.');
      setLoading(false);
    }
  };

  // Handle load more customers
  const handleLoadMore = () => {
    console.log('📱 SMS: Load more triggered:', { loadingMore, allLoaded, hasRef: !!measurementsRef.current });
    if (!loadingMore && !allLoaded && measurementsRef.current) {
      console.log('🔄 SMS: Calling loadMore...');
      measurementsRef.current.loadMore();
    } else {
      console.log('🚫 SMS: Not calling loadMore:', { loadingMore, allLoaded, hasRef: !!measurementsRef.current });
    }
  };

  if (loading) {
    return (
      <YStack flex={1} backgroundColor={colors.background} justifyContent="center" alignItems="center">
        <ActivityIndicator size="large" color={colors.primary} />
        <Text color={colors.text} marginTop="$4">Loading customers...</Text>
      </YStack>
    );
  }

  // Error state
  if (error) {
    return (
      <YStack flex={1} backgroundColor={colors.background} justifyContent="center" alignItems="center" padding="$4">
        <View
          style={{
            width: 80,
            height: 80,
            borderRadius: 40,
            backgroundColor: `${colors.error}15`,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 24,
          }}
        >
          <Text fontSize={32}>⚠️</Text>
        </View>
        <Text
          color={colors.text}
          fontSize={18}
          fontFamily="$heading"
          fontWeight="600"
          textAlign="center"
          marginBottom="$3"
        >
          Unable to Load Customers
        </Text>
        <Text
          color={colors.textSecondary}
          fontSize={14}
          fontFamily="$body"
          textAlign="center"
          marginBottom="$6"
          lineHeight={20}
        >
          {error}
        </Text>
        <Button
          size="$4"
          backgroundColor={colors.primary}
          color={colors.card}
          onPress={handleRetry}
          borderRadius={borderRadius.medium}
        >
          Try Again
        </Button>
      </YStack>
    );
  }

  return (
    <YStack f={1} style={{ backgroundColor: colors.background }}>
      {/* Clean Professional Header */}
      <YStack
        style={{
          backgroundColor: colors.primary,
          borderBottomLeftRadius: borderRadius.extraLarge,
          borderBottomRightRadius: borderRadius.extraLarge,
        }}
      >
        <YStack
          paddingHorizontal={spacing.lg}
          paddingTop={spacing.xl + 10}
          paddingBottom={spacing.xl}
        >
          {/* Header Content */}
          <XStack justifyContent="space-between" alignItems="center">
            <YStack flex={1}>
              <Text
                color="rgba(255, 255, 255, 0.8)"
                fontSize={14}
                fontWeight="600"
                fontFamily="$body"
                letterSpacing={0.5}
                textTransform="uppercase"
                marginBottom="$2"
              >
                {TEXT_CONSTANTS.EMOJI_SMS} Communication
              </Text>
              <H1
                color={colors.textLight}
                fontSize={32}
                fontWeight="800"
                fontFamily="$heading"
                letterSpacing={-1.2}
                lineHeight={36}
              >
                {TEXT_CONSTANTS.SMS_SCREEN_TITLE}
              </H1>
            </YStack>

            {/* Customer Count Badge */}
            <View
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                borderRadius: borderRadius.large,
                paddingHorizontal: spacing.lg,
                paddingVertical: spacing.md,
                borderWidth: 1,
                borderColor: 'rgba(255, 255, 255, 0.25)',
                minWidth: 100,
              }}
            >
              <Text
                color="rgba(255, 255, 255, 0.8)"
                fontSize={12}
                fontWeight="600"
                fontFamily="$body"
                textAlign="center"
                marginBottom="$1"
                textTransform="uppercase"
                letterSpacing={0.5}
              >
                Customers
              </Text>
              <Text
                color={colors.textLight}
                fontSize={24}
                fontWeight="800"
                fontFamily="$heading"
                textAlign="center"
                letterSpacing={-0.8}
              >
                {customers.length}
              </Text>
            </View>
          </XStack>
        </YStack>
      </YStack>

      {/* Content */}
      <YStack flex={1} paddingHorizontal={spacing.lg} paddingTop={spacing.md} gap="$4">
        {/* Templates Section */}
        <YStack gap="$3">
          <XStack alignItems="center" justifyContent="space-between">
            <Text
              color={colors.text}
              fontSize={18}
              fontWeight="700"
              fontFamily="$heading"
              letterSpacing={-0.5}
            >
              Message Templates
            </Text>
            {selectedTemplate && (
              <Button
                size="$2"
                variant="outlined"
                borderColor={colors.border}
                color={colors.textSecondary}
                onPress={clearTemplate}
              >
                Clear
              </Button>
            )}
          </XStack>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <XStack gap="$3" paddingHorizontal="$1">
              {SMS_TEMPLATES.map((template) => (
                <TouchableOpacity
                  key={template.id}
                  onPress={() => applyTemplate(template)}
                >
                  <Card
                    padding={spacing.lg}
                    backgroundColor={selectedTemplate?.id === template.id ? colors.primary : colors.card}
                    borderColor={selectedTemplate?.id === template.id ? colors.primary : colors.border}
                    borderWidth={selectedTemplate?.id === template.id ? 2 : 1}
                    borderRadius={borderRadius.card}
                    minWidth={170}
                    pressStyle={{
                      backgroundColor: selectedTemplate?.id === template.id ? colors.primaryDark : colors.backgroundAlt,
                      transform: [{ scale: 0.97 }]
                    }}
                  >
                    <YStack gap="$2" alignItems="center">
                      <Text
                        color={selectedTemplate?.id === template.id ? colors.card : colors.text}
                        fontWeight="700"
                        fontSize={16}
                        fontFamily="$heading"
                        textAlign="center"
                        letterSpacing={-0.4}
                      >
                        {template.name}
                      </Text>
                      <Text
                        color={selectedTemplate?.id === template.id ? 'rgba(255, 255, 255, 0.85)' : colors.textSecondary}
                        fontSize={13}
                        fontFamily="$body"
                        textAlign="center"
                        numberOfLines={2}
                        lineHeight={18}
                        fontWeight="500"
                      >
                        {template.description}
                      </Text>
                    </YStack>
                  </Card>
                </TouchableOpacity>
              ))}
            </XStack>
          </ScrollView>
        </YStack>

        {/* Message Input */}
        <YStack gap="$3">
          <XStack alignItems="center" justifyContent="space-between">
            <Text
              color={colors.text}
              fontSize={18}
              fontWeight="700"
              fontFamily="$heading"
              letterSpacing={-0.5}
            >
              Message
            </Text>
            {selectedTemplate && (
              <View
                style={{
                  backgroundColor: `${colors.primary}10`,
                  paddingHorizontal: spacing.sm,
                  paddingVertical: spacing.xs,
                  borderRadius: borderRadius.tag,
                  borderWidth: 1,
                  borderColor: `${colors.primary}25`,
                }}
              >
                <Text
                  color={colors.primary}
                  fontSize={11}
                  fontFamily="$body"
                  fontWeight="600"
                  textTransform="uppercase"
                  letterSpacing={0.5}
                >
                  {selectedTemplate.name}
                </Text>
              </View>
            )}
          </XStack>
          <View
            style={{
              backgroundColor: colors.card,
              borderColor: colors.border,
              borderWidth: 1,
              borderRadius: borderRadius.input,
              paddingHorizontal: spacing.lg,
              paddingVertical: spacing.md,
              minHeight: 130,
            }}
          >
            <TextInput
              key={`message-input-${selectedTemplate?.id || 'none'}`}
              placeholder={TEXT_CONSTANTS.SMS_MESSAGE_PLACEHOLDER}
              value={message}
              onChangeText={(text: string) => {
                setMessage(text);

                // Clear template if user manually edits and text doesn't match template
                if (selectedTemplate && text !== selectedTemplate.message) {
                  setSelectedTemplate(null);
                }
              }}
              style={{
                fontSize: 16,
                color: colors.text,
                fontFamily: 'Poppins_400Regular',
                minHeight: 100,
                textAlignVertical: 'top',
              }}
              placeholderTextColor={colors.textSecondary}
              multiline={true}
              numberOfLines={6}
              autoCapitalize="sentences"
              autoCorrect={true}
            />
          </View>
          {selectedTemplate && message.includes('{') && (
            <Card
              padding={spacing.lg}
              backgroundColor={`${colors.info}08`}
              borderColor={`${colors.info}30`}
              borderWidth={1}
              borderRadius={borderRadius.card}
            >
              <XStack alignItems="center" gap="$3" marginBottom="$3">
                <View
                  style={{
                    width: 24,
                    height: 24,
                    borderRadius: 12,
                    backgroundColor: `${colors.info}20`,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Text fontSize={14}>👁️</Text>
                </View>
                <Text
                  color={colors.info}
                  fontSize={13}
                  fontFamily="$body"
                  fontWeight="700"
                  textTransform="uppercase"
                  letterSpacing={0.5}
                >
                  Message Preview
                </Text>
              </XStack>
              <Text
                color={colors.text}
                fontSize={15}
                fontFamily="$body"
                lineHeight={22}
                style={{
                  fontStyle: 'italic',
                }}
              >
                {replaceTemplateVariables(message, {
                  id: 'sample',
                  name: 'John Doe',
                  phoneNo: '+91 9876543210',
                  billNo: 'B001',
                  deliveryDate: '25-12-2024'
                })}
              </Text>
            </Card>
          )}
        </YStack>

        {/* Customer Selection Header */}
        <XStack alignItems="center" justifyContent="space-between" marginBottom="$2">
          <YStack>
            <Text
              color={colors.text}
              fontSize={20}
              fontWeight="800"
              fontFamily="$heading"
              letterSpacing={-0.6}
              marginBottom="$1"
            >
              {TEXT_CONSTANTS.SMS_SELECT_CUSTOMERS}
            </Text>
            <Text
              color={colors.textSecondary}
              fontSize={14}
              fontFamily="$body"
              fontWeight="600"
            >
              {selectedCustomers.length} of {filteredCustomers.length} selected
            </Text>
          </YStack>
          <Button
            size="$3"
            variant="outlined"
            borderColor={selectedCustomers.length === filteredCustomers.length ? colors.primary : colors.border}
            color={selectedCustomers.length === filteredCustomers.length ? colors.primary : colors.textSecondary}
            onPress={selectAllCustomers}
            borderRadius={borderRadius.button}
            borderWidth={2}
            fontWeight="600"
            pressStyle={{
              backgroundColor: colors.backgroundAlt,
              transform: [{ scale: 0.96 }]
            }}
          >
            {selectedCustomers.length === filteredCustomers.length ? 'Deselect All' : 'Select All'}
          </Button>
        </XStack>

        {/* Professional Search Input */}
        <View
          style={{
            backgroundColor: colors.card,
            borderColor: colors.border,
            borderWidth: 1,
            borderRadius: borderRadius.input,
            paddingHorizontal: spacing.lg,
            paddingVertical: spacing.md,
          }}
        >
          <TextInput
            placeholder="Search customers by name, phone, or bill number..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={{
              fontSize: 16,
              color: colors.text,
              fontFamily: 'Poppins_400Regular',
              minHeight: 24,
              // Android-specific fixes to remove underline
              textDecorationLine: 'none',
              borderBottomWidth: 0,
              borderBottomColor: 'transparent',
            }}
            placeholderTextColor={colors.textSecondary}
            autoCapitalize="none"
            autoCorrect={false}
            underlineColorAndroid="transparent"
          />
        </View>

        {/* Customer List - Using FlatList with batch loading */}
        {filteredCustomers.length > 0 ? (
          <FlatList
            data={filteredCustomers}
            renderItem={renderCustomerItem}
            keyExtractor={(item) => item.id}
            style={{ flex: 1 }}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            ListFooterComponent={
              loadingMore ? (
                <YStack padding="$4" alignItems="center">
                  <ActivityIndicator size="small" color={colors.primary} />
                  <Text
                    marginTop="$2"
                    color={colors.textSecondary}
                    fontSize={14}
                    fontFamily="$body"
                    fontWeight="500"
                  >
                    Loading more customers...
                  </Text>
                </YStack>
              ) : allLoaded && customers.length > 0 ? (
                <YStack padding="$4" alignItems="center">
                  <View
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: `${colors.primary}10`,
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}
                  >
                    <Text fontSize={16}>✓</Text>
                  </View>
                  <Text
                    color={colors.textSecondary}
                    fontSize={12}
                    fontFamily="$body"
                    fontWeight="500"
                  >
                    All customers loaded
                  </Text>
                </YStack>
              ) : !allLoaded && customers.length > 0 ? (
                <YStack padding="$4" alignItems="center">
                  <Button
                    size="$3"
                    variant="outlined"
                    onPress={handleLoadMore}
                    disabled={loadingMore}
                  >
                    {loadingMore ? 'Loading...' : 'Load More Customers'}
                  </Button>
                </YStack>
              ) : null
            }
          />
        ) : (
          <YStack
            flex={1}
            justifyContent="center"
            alignItems="center"
            paddingVertical={spacing.xl}
          >
            {/* Empty State Icon */}
            <View
              style={{
                width: UI_CONSTANTS.AVATAR_SIZE_EXTRA_LARGE * 0.67,
                height: UI_CONSTANTS.AVATAR_SIZE_EXTRA_LARGE * 0.67,
                borderRadius: UI_CONSTANTS.BORDER_RADIUS_LARGE,
                backgroundColor: `${colors.primary}15`,
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: spacing.lg,
              }}
            >
              <Text fontSize={UI_CONSTANTS.FONT_SIZE_HERO}>
                {customers.length === 0 ? TEXT_CONSTANTS.EMOJI_RULER : TEXT_CONSTANTS.EMOJI_SEARCH}
              </Text>
            </View>

            {/* Title and Description */}
            <Text
              color={colors.text}
              fontSize={20}
              fontWeight="700"
              marginBottom="$2"
              fontFamily="$heading"
              textAlign="center"
              letterSpacing={-0.5}
            >
              {customers.length === 0
                ? loadingMore
                  ? 'Loading Customers...'
                  : 'No Customers Found'
                : 'No Search Results'}
            </Text>
            <Text
              color={colors.textSecondary}
              fontSize={14}
              fontFamily="$body"
              textAlign="center"
              lineHeight={20}
              marginBottom="$4"
              paddingHorizontal={spacing.lg}
            >
              {customers.length === 0
                ? loadingMore
                  ? 'Please wait while we load your customer data...'
                  : 'Add some measurements first to see customers here.'
                : 'Try adjusting your search terms or load more data.'}
            </Text>
            {customers.length === 0 && !loadingMore && !allLoaded && (
              <Button
                size="$3"
                variant="outlined"
                borderColor={colors.border}
                color={colors.textSecondary}
                onPress={handleLoadMore}
                borderRadius={borderRadius.button}
              >
                Load More Data
              </Button>
            )}
          </YStack>
        )}

        {/* Professional Send Button */}
        <YStack paddingBottom={spacing.lg} paddingTop="$2">
          <Button
            size="$5"
            backgroundColor={colors.primary}
            color={colors.card}
            onPress={handleSendSms}
            disabled={sending || selectedCustomers.length === 0 || !message.trim()}
            opacity={sending || selectedCustomers.length === 0 || !message.trim() ? 0.6 : 1}
            borderRadius={borderRadius.button}
            fontWeight="700"
            fontSize={17}
            fontFamily="$heading"
            letterSpacing={-0.4}
            icon={sending ? undefined : SendIcon}
            pressStyle={{
              backgroundColor: colors.primaryDark,
              transform: [{ scale: 0.98 }]
            }}
          >
            {sending ? 'Sending SMS...' : TEXT_CONSTANTS.SMS_SEND_BUTTON}
          </Button>
          {selectedCustomers.length > 0 && (
            <Text
              color={colors.textSecondary}
              fontSize={14}
              fontFamily="$body"
              textAlign="center"
              marginTop="$3"
              fontWeight="500"
            >
              Will send to {selectedCustomers.length} customer{selectedCustomers.length > 1 ? 's' : ''}
            </Text>
          )}
        </YStack>
      </YStack>
    </YStack>
  );
};

export default SmsScreen;
