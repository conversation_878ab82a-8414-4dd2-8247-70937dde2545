import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  orderBy,
  limit,
  where,
  onSnapshot,
  startAfter,
  writeBatch
} from 'firebase/firestore';
import { db } from '../../firebaseConfig';
import { startTimer, endTimer } from '../utils/performanceMonitor';
import { TEXT_CONSTANTS, BUSINESS_CONSTANTS } from '../constants';
import { getCountFromServer } from 'firebase/firestore';

const MEASUREMENTS_COLLECTION = 'measurements';

// Cache for connection status to avoid repeated tests
let connectionCache = { isConnected: null, lastChecked: 0 };

/**
 * Test Firebase connection with caching
 * @returns {Promise<boolean>} True if connection is successful
 */
export const testFirebaseConnection = async () => {
  const now = Date.now();

  // Return cached result if recent
  if (connectionCache.isConnected !== null &&
      (now - connectionCache.lastChecked) < BUSINESS_CONSTANTS.CONNECTION_CACHE_DURATION) {
    console.log('🔄 Using cached connection status:', connectionCache.isConnected);
    return connectionCache.isConnected;
  }

  try {
    console.log('🔍 Testing Firebase connection...');
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Use a more efficient query with limit for connection test
    const q = query(measurementsRef, limit(1));
    await getDocs(q);

    connectionCache = { isConnected: true, lastChecked: now };
    console.log('✅ Firebase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    connectionCache = { isConnected: false, lastChecked: now };
    return false;
  }
};

/**
 * Get all measurements from Firestore
 * @returns {Promise<Array>} Array of measurement objects
 */
export const getAllMeasurements = async () => {
  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    // Try to get measurements ordered by date, but handle if date field doesn't exist
    let q;
    try {
      q = query(measurementsRef, orderBy('date', 'desc'));
    } catch (err) {
      console.warn('Could not order by date, using default order:', err);
      q = query(measurementsRef);
    }

    const querySnapshot = await getDocs(q);

    const measurements = [];
    querySnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        // Ensure required fields exist and map to correct field names
        const measurement = {
          id: doc.id,
          name: data.name || TEXT_CONSTANTS.DEFAULT_CUSTOMER_NAME,
          phoneNo: data.phoneNo || '',
          billNo: data.billNo || '',
          date: data.date || new Date().toISOString().split('T')[0],
          deliveryDate: data.deliveryDate || '',
          imageUrl: data.imageUrl || '',
          measurements: data.measurements || {},
          notes: data.notes || ''
        };

        measurements.push(measurement);
      } catch (docError) {
        console.error('Error processing document:', docError);
        // Continue with next document instead of failing the whole operation
      }
    });

    return measurements;
  } catch (error) {
    console.error('Error getting measurements:', error);
    throw error;
  }
};

/**
 * Get a single measurement by ID
 * @param {string} id - Measurement document ID
 * @returns {Promise<Object>} Measurement object
 */
export const getMeasurementById = async (id) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();

      // Ensure required fields exist and map to correct field names
      return {
        id: docSnap.id,
        name: data.name || TEXT_CONSTANTS.DEFAULT_CUSTOMER_NAME,
        phoneNo: data.phoneNo || '',
        billNo: data.billNo || '',
        date: data.date || new Date().toISOString().split('T')[0],
        deliveryDate: data.deliveryDate || '',
        imageUrl: data.imageUrl || '',
        measurements: data.measurements || {},
        notes: data.notes || ''
      };
    } else {
      throw new Error('Measurement not found');
    }
  } catch (error) {
    console.error('Error getting measurement:', error);
    throw error;
  }
};

// Search cache for performance optimization
const searchCache = new Map();
const SEARCH_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const SEARCH_CACHE_MAX_SIZE = 50; // Maximum number of cached searches

/**
 * Clear expired search cache entries
 */
const clearExpiredSearchCache = () => {
  const now = Date.now();
  for (const [key, value] of searchCache.entries()) {
    if (now - value.timestamp > SEARCH_CACHE_TTL) {
      searchCache.delete(key);
    }
  }
};

/**
 * Get cached search results if available and not expired
 * @param {string} searchTerm - Search term to look up
 * @returns {Array|null} Cached results or null if not found/expired
 */
const getCachedSearchResults = (searchTerm) => {
  clearExpiredSearchCache();
  const cacheKey = searchTerm.toLowerCase().trim();
  const cached = searchCache.get(cacheKey);

  if (cached && (Date.now() - cached.timestamp) < SEARCH_CACHE_TTL) {
    console.log('🎯 Using cached search results for:', searchTerm);
    return cached.results;
  }

  return null;
};

/**
 * Cache search results
 * @param {string} searchTerm - Search term
 * @param {Array} results - Search results to cache
 */
const cacheSearchResults = (searchTerm, results) => {
  // Limit cache size
  if (searchCache.size >= SEARCH_CACHE_MAX_SIZE) {
    const firstKey = searchCache.keys().next().value;
    searchCache.delete(firstKey);
  }

  const cacheKey = searchTerm.toLowerCase().trim();
  searchCache.set(cacheKey, {
    results: results,
    timestamp: Date.now()
  });
};

/**
 * Generate search tokens for a document
 * @param {Object} data - Document data
 * @returns {Array} Array of search tokens
 */
const generateSearchTokens = (data) => {
  const tokens = new Set();

  // Add name tokens (split by spaces, convert to lowercase)
  if (data.name) {
    const nameTokens = data.name.toLowerCase().split(/\s+/).filter(token => token.length >= 2);
    nameTokens.forEach(token => tokens.add(token));

    // Add partial name tokens for better matching
    nameTokens.forEach(token => {
      for (let i = 2; i <= token.length; i++) {
        tokens.add(token.substring(0, i));
      }
    });
  }

  // Add phone number (full and partial)
  if (data.phoneNo) {
    tokens.add(data.phoneNo.toLowerCase());
    // Add phone number without spaces/dashes
    const cleanPhone = data.phoneNo.replace(/[\s\-\(\)]/g, '');
    if (cleanPhone.length >= 3) {
      tokens.add(cleanPhone);
      // Add partial phone numbers
      for (let i = 3; i <= cleanPhone.length; i++) {
        tokens.add(cleanPhone.substring(0, i));
      }
    }
  }

  // Add bill number (full and partial)
  if (data.billNo) {
    tokens.add(data.billNo.toLowerCase());
    const billNo = data.billNo.toLowerCase();
    for (let i = 2; i <= billNo.length; i++) {
      tokens.add(billNo.substring(0, i));
    }
  }

  return Array.from(tokens);
};

/**
 * Sort measurements by delivery date priority
 * @param {Object} a - First measurement object
 * @param {Object} b - Second measurement object
 * @returns {number} Sort comparison result
 */
const sortByDeliveryPriority = (a, b) => {
  // Prioritize by delivery date if available, otherwise use creation timestamp
  const deliveryTimestampA = a.deliveryTimestamp || a.timestamp || 0;
  const deliveryTimestampB = b.deliveryTimestamp || b.timestamp || 0;
  return deliveryTimestampB - deliveryTimestampA; // Newest delivery dates first
};

/**
 * Optimized search with multiple Firestore query strategies
 * @param {string} searchTerm - Search term to match against customer name, phone, or bill number
 * @param {Function} onProgress - Optional callback for progressive results (partial results as they come)
 * @returns {Promise<Array>} Array of matching measurement objects
 */
export const searchAllMeasurements = async (searchTerm, onProgress = null) => {
  try {
    console.log('🔍 Advanced Firestore search for:', searchTerm);
    startTimer('search_performance');

    if (!searchTerm || searchTerm.trim() === '') {
      return [];
    }

    // Check cache first
    const cachedResults = getCachedSearchResults(searchTerm);
    if (cachedResults) {
      endTimer('search_performance');
      return cachedResults;
    }

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const normalizedSearchTerm = searchTerm.toLowerCase().trim();
    const matchingMeasurements = new Set(); // Use Set to avoid duplicates

    // Helper function to parse date and create timestamp for sorting
    const createTimestamp = (dateStr) => {
      if (!dateStr) return Date.now();
      let parsedDate;
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts[0].length === 4) {
          parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
        } else {
          parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }
      } else {
        parsedDate = new Date(dateStr);
      }
      return parsedDate.getTime();
    };

    // Helper function to process document and add to results
    const processDocument = (doc) => {
      try {
        const data = doc.data();
        const measurement = {
          id: doc.id,
          name: data.name || TEXT_CONSTANTS.DEFAULT_CUSTOMER_NAME,
          phoneNo: data.phoneNo || '',
          billNo: data.billNo || '',
          date: data.date || new Date().toISOString().split('T')[0],
          deliveryDate: data.deliveryDate || '',
          imageUrl: data.imageUrl || '',
          measurements: data.measurements || {},
          notes: data.notes || '',
          timestamp: data.timestamp || createTimestamp(data.date),
          deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date)
        };
        matchingMeasurements.add(JSON.stringify(measurement)); // Use JSON string as Set key to avoid duplicates
        return measurement;
      } catch (docError) {
        console.error('❌ Error processing document:', docError);
        return null;
      }
    };

    // Strategy 1: Search by exact phone number match (fastest)
    if (/^\d+$/.test(normalizedSearchTerm)) {
      try {
        console.log('🚀 Strategy 1: Exact phone number search...');
        const phoneQuery = query(
          measurementsRef,
          where('phoneNo', '==', normalizedSearchTerm),
          limit(50)
        );

        const phoneSnapshot = await getDocs(phoneQuery);
        console.log(`📱 Phone query found ${phoneSnapshot.size} exact matches`);

        phoneSnapshot.forEach(processDocument);

        if (phoneSnapshot.size > 0) {
          console.log('✅ Found exact phone matches, returning early');
          const results = Array.from(matchingMeasurements).map(str => JSON.parse(str));
          results.sort(sortByDeliveryPriority);
          cacheSearchResults(searchTerm, results);
          endTimer('search_performance');
          return results;
        }
      } catch (error) {
        console.log('⚠️ Phone search failed:', error.message);
      }
    }

    // Strategy 2: Search by exact bill number match
    try {
      console.log('🚀 Strategy 2: Exact bill number search...');
      const billQuery = query(
        measurementsRef,
        where('billNo', '==', normalizedSearchTerm),
        limit(50)
      );

      const billSnapshot = await getDocs(billQuery);
      console.log(`🧾 Bill query found ${billSnapshot.size} exact matches`);

      billSnapshot.forEach(processDocument);

      if (billSnapshot.size > 0) {
        console.log('✅ Found exact bill matches, returning early');
        const results = Array.from(matchingMeasurements).map(str => JSON.parse(str));
        results.sort(sortByDeliveryPriority);
        cacheSearchResults(searchTerm, results);
        endTimer('search_performance');
        return results;
      }
    } catch (error) {
      console.log('⚠️ Bill search failed:', error.message);
    }

    // Strategy 3: Advanced name prefix search with multiple variations
    if (normalizedSearchTerm.length >= 2) {
      try {
        console.log('🚀 Strategy 3: Advanced name prefix search...');

        // Create multiple search variations
        const searchVariations = [
          normalizedSearchTerm,
          normalizedSearchTerm.charAt(0).toUpperCase() + normalizedSearchTerm.slice(1), // Capitalize first letter
          normalizedSearchTerm.toUpperCase(), // All uppercase
        ];

        // Use 'in' operator for multiple exact matches (more efficient than multiple queries)
        const nameInQuery = query(
          measurementsRef,
          where('name', 'in', searchVariations),
          limit(50)
        );

        const nameInSnapshot = await getDocs(nameInQuery);
        console.log(`👤 Name 'in' query found ${nameInSnapshot.size} exact matches`);
        nameInSnapshot.forEach(processDocument);

        // Also try prefix search for partial matches
        const namePrefixQuery = query(
          measurementsRef,
          orderBy('name'),
          where('name', '>=', normalizedSearchTerm),
          where('name', '<=', normalizedSearchTerm + '\uf8ff'),
          limit(100)
        );

        const namePrefixSnapshot = await getDocs(namePrefixQuery);
        console.log(`📊 Name prefix query found ${namePrefixSnapshot.size} potential matches`);

        // Process prefix matches with additional filtering
        namePrefixSnapshot.forEach((doc) => {
          try {
            const data = doc.data();
            const name = (data.name || '').toLowerCase();
            const phoneNo = data.phoneNo || '';
            const billNo = data.billNo || '';

            // More precise matching for prefix results
            if (name.includes(normalizedSearchTerm) ||
                phoneNo.includes(normalizedSearchTerm) ||
                billNo.includes(normalizedSearchTerm)) {
              processDocument(doc);
            }
          } catch (docError) {
            console.error('❌ Error processing prefix query document:', docError);
          }
        });

        console.log(`✅ Strategy 3 completed: ${matchingMeasurements.size} total matches found`);

      } catch (error) {
        console.log('⚠️ Name search strategies failed:', error.message);
      }
    }

    // Strategy 4: Search tokens with array-contains-any (fastest for tokenized search)
    if (normalizedSearchTerm.length >= 2) {
      try {
        console.log('🚀 Strategy 4: Search tokens with array-contains-any...');

        // Generate search tokens for the search term
        const searchTokens = generateSearchTokens({
          name: normalizedSearchTerm,
          phoneNo: normalizedSearchTerm,
          billNo: normalizedSearchTerm
        });

        // Use array-contains-any for fast token-based search (limit to 10 tokens max)
        const limitedTokens = searchTokens.slice(0, 10);

        if (limitedTokens.length > 0) {
          const tokenQuery = query(
            measurementsRef,
            where('searchTokens', 'array-contains-any', limitedTokens),
            limit(100)
          );

          const tokenSnapshot = await getDocs(tokenQuery);
          console.log(`🔍 Token query found ${tokenSnapshot.size} potential matches`);

          tokenSnapshot.forEach(processDocument);
        }

      } catch (error) {
        console.log('⚠️ Token search failed (documents may not have searchTokens yet):', error.message);
      }
    }

    // Strategy 5: Partial phone number search (for longer search terms)
    if (/\d/.test(normalizedSearchTerm) && normalizedSearchTerm.length >= 3) {
      try {
        console.log('🚀 Strategy 5: Partial phone number search...');

        // Extract digits from search term
        const digits = normalizedSearchTerm.replace(/\D/g, '');

        if (digits.length >= 3) {
          // Search for phone numbers that start with these digits
          const phoneRangeQuery = query(
            measurementsRef,
            orderBy('phoneNo'),
            where('phoneNo', '>=', digits),
            where('phoneNo', '<=', digits + '\uf8ff'),
            limit(100)
          );

          const phoneRangeSnapshot = await getDocs(phoneRangeQuery);
          console.log(`📱 Phone range query found ${phoneRangeSnapshot.size} potential matches`);

          phoneRangeSnapshot.forEach((doc) => {
            try {
              const data = doc.data();
              const phoneNo = data.phoneNo || '';

              // Check if phone contains the search digits
              if (phoneNo.includes(digits) || phoneNo.includes(normalizedSearchTerm)) {
                processDocument(doc);
              }
            } catch (docError) {
              console.error('❌ Error processing phone range document:', docError);
            }
          });
        }

      } catch (error) {
        console.log('⚠️ Partial phone search failed:', error.message);
      }
    }

    // Convert Set back to Array and remove duplicates
    const uniqueResults = Array.from(matchingMeasurements).map(str => JSON.parse(str));

    // If we have results from optimized queries, return them
    if (uniqueResults.length > 0) {
      console.log(`✅ Advanced search found ${uniqueResults.length} results using optimized queries`);
      uniqueResults.sort(sortByDeliveryPriority);
      cacheSearchResults(searchTerm, uniqueResults);
      endTimer('search_performance');
      return uniqueResults;
    }

    // Strategy 6: Limited fallback scan (only if no results from optimized queries)
    console.log('⚠️ No results from optimized queries, performing limited fallback scan...');

    // Use a more targeted fallback - limit the scan to recent documents
    const fallbackQuery = query(
      measurementsRef,
      orderBy('timestamp', 'desc'),
      limit(500) // Limit fallback scan to most recent 500 documents
    );

    const fallbackSnapshot = await getDocs(fallbackQuery);
    console.log(`📊 Fallback scan processing ${fallbackSnapshot.size} recent documents...`);

    const fallbackResults = new Set();
    let processedCount = 0;
    const batchSize = 50; // Process in batches for better UX

    fallbackSnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        const name = (data.name || '').toLowerCase();
        const phoneNo = data.phoneNo || '';
        const billNo = data.billNo || '';

        // More comprehensive matching for fallback (including notes)
        if (name.includes(normalizedSearchTerm) ||
            phoneNo.includes(normalizedSearchTerm) ||
            billNo.includes(normalizedSearchTerm) ||
            (data.notes && data.notes.toLowerCase().includes(normalizedSearchTerm))) {

          const measurement = {
            id: doc.id,
            name: data.name || TEXT_CONSTANTS.DEFAULT_CUSTOMER_NAME,
            phoneNo: data.phoneNo || '',
            billNo: data.billNo || '',
            date: data.date || new Date().toISOString().split('T')[0],
            deliveryDate: data.deliveryDate || '',
            imageUrl: data.imageUrl || '',
            measurements: data.measurements || {},
            notes: data.notes || '',
            timestamp: data.timestamp || createTimestamp(data.date),
            deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date)
          };

          fallbackResults.add(JSON.stringify(measurement));
        }

        processedCount++;

        // Send progressive results every batch
        if (onProgress && processedCount % batchSize === 0) {
          const currentResults = Array.from(fallbackResults).map(str => JSON.parse(str));
          const sortedPartial = currentResults.sort(sortByDeliveryPriority);
          onProgress(sortedPartial, false); // false = not complete
        }

      } catch (docError) {
        console.error('❌ Error processing document during fallback scan:', docError);
      }
    });

    const finalResults = Array.from(fallbackResults).map(str => JSON.parse(str));
    console.log(`📊 Fallback scan completed. Found ${finalResults.length} matching measurements`);

    // Sort final results by delivery date priority (upcoming first)
    finalResults.sort(sortByDeliveryPriority);

    // Cache results for future use
    cacheSearchResults(searchTerm, finalResults);

    console.log(`✅ Advanced search completed. Found ${finalResults.length} matching measurements`);
    endTimer('search_performance');

    // Send final results if using progressive callback
    if (onProgress) {
      onProgress(finalResults, true); // true = complete
    }

    return finalResults;

  } catch (error) {
    console.error('❌ Error in optimized search:', error);
    endTimer('search_performance');
    throw error;
  }
};

/**
 * Clear search cache (useful when data changes)
 */
export const clearSearchCache = () => {
  searchCache.clear();
  console.log('🧹 Search cache cleared');
};

/**
 * Get search cache statistics
 * @returns {Object} Cache statistics
 */
export const getSearchCacheStats = () => {
  clearExpiredSearchCache();
  return {
    size: searchCache.size,
    maxSize: SEARCH_CACHE_MAX_SIZE,
    ttlMinutes: SEARCH_CACHE_TTL / (60 * 1000),
    entries: Array.from(searchCache.keys())
  };
};

/**
 * Search measurements by customer name (legacy function - kept for compatibility)
 * @param {string} searchTerm - Search term to match against customer name
 * @returns {Promise<Array>} Array of matching measurement objects
 */
export const searchMeasurementsByName = async (searchTerm) => {
  return searchAllMeasurements(searchTerm);
};

/**
 * Add a new measurement to Firestore
 * @param {Object} measurementData - Measurement data object
 * @returns {Promise<string>} ID of the newly created measurement
 */
export const addMeasurement = async (measurementData) => {
  try {
    // Validate that this is not test data being added automatically
    if (!measurementData.name || !measurementData.phoneNo) {
      throw new Error('Invalid measurement data: name and phoneNo are required');
    }

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Generate search tokens for better search performance
    const searchTokens = generateSearchTokens(measurementData);

    const docRef = await addDoc(measurementsRef, {
      ...measurementData,
      date: measurementData.date || new Date().toISOString().split('T')[0],
      timestamp: Date.now(), // Add timestamp for proper sorting
      searchTokens: searchTokens // Add search tokens for optimized queries
    });

    // Clear search cache since new data was added
    clearSearchCache();

    return docRef.id;
  } catch (error) {
    console.error('Error adding measurement:', error);
    throw error;
  }
};

/**
 * Update an existing measurement
 * @param {string} id - Measurement document ID
 * @param {Object} measurementData - Updated measurement data
 * @returns {Promise<void>}
 */
export const updateMeasurement = async (id, measurementData) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);

    // Helper function to parse date and create timestamp for sorting
    const createTimestamp = (dateStr) => {
      if (!dateStr) return Date.now();
      let parsedDate;
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts[0].length === 4) {
          // YYYY-MM-DD format
          parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
        } else {
          // DD-MM-YYYY format
          parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }
      } else {
        parsedDate = new Date(dateStr);
      }
      return parsedDate.getTime();
    };

    // Generate updated search tokens
    const searchTokens = generateSearchTokens(measurementData);

    // Calculate delivery timestamp if delivery date is being updated
    const updateData = {
      ...measurementData,
      searchTokens: searchTokens // Update search tokens for optimized queries
    };

    // If delivery date is being updated, recalculate delivery timestamp
    if (measurementData.deliveryDate) {
      updateData.deliveryTimestamp = createTimestamp(measurementData.deliveryDate);
    }

    await updateDoc(docRef, updateData);

    // Clear search cache since data was updated
    clearSearchCache();
  } catch (error) {
    console.error('Error updating measurement:', error);
    throw error;
  }
};

/**
 * Delete a measurement
 * @param {string} id - Measurement document ID
 * @returns {Promise<void>}
 */
export const deleteMeasurement = async (id) => {
  try {
    const docRef = doc(db, MEASUREMENTS_COLLECTION, id);
    await deleteDoc(docRef);

    // Clear search cache since data was deleted
    clearSearchCache();
  } catch (error) {
    console.error('Error deleting measurement:', error);
    throw error;
  }
};

/**
 * Delete all measurements with deliveryDate in a specific year
 * @param {number} year - Year to delete (e.g., 2020)
 * @param {Function} onProgress - Optional callback for progress updates
 * @returns {Promise<{deletedCount: number, errors: Array}>} Result summary
 */
export const deleteMeasurementsByYear = async (year, onProgress = null) => {
  try {
    console.log(`🗑️ Starting bulk delete for year ${year}...`);
    startTimer('bulk_delete_performance');

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Helper function to check if a date string is in the specified year
    const isDateInYear = (dateStr) => {
      if (!dateStr) return false;

      try {
        let parsedYear;
        if (dateStr.includes('-')) {
          const parts = dateStr.split('-');
          if (parts[0].length === 4) {
            // YYYY-MM-DD format
            parsedYear = parseInt(parts[0]);
          } else {
            // DD-MM-YYYY format
            parsedYear = parseInt(parts[2]);
          }
        } else {
          // Try to parse as date
          const parsedDate = new Date(dateStr);
          parsedYear = parsedDate.getFullYear();
        }

        return parsedYear === year;
      } catch (error) {
        console.error('Error parsing date:', dateStr, error);
        return false;
      }
    };

    // First, find all documents with deliveryDate in the specified year
    console.log(`🔍 Finding all measurements with deliveryDate in ${year}...`);
    const allSnapshot = await getDocs(measurementsRef);
    console.log(`📊 Scanning ${allSnapshot.size} total measurements...`);

    const documentsToDelete = [];
    let scannedCount = 0;

    allSnapshot.forEach((doc) => {
      try {
        const data = doc.data();
        scannedCount++;

        if (isDateInYear(data.deliveryDate)) {
          documentsToDelete.push({
            id: doc.id,
            name: data.name || 'Unknown',
            deliveryDate: data.deliveryDate,
            billNo: data.billNo || ''
          });
        }

        // Report progress every 50 documents for more frequent updates
        if (onProgress && (scannedCount % 50 === 0 || scannedCount === allSnapshot.size)) {
          onProgress({
            phase: 'scanning',
            scannedCount,
            totalCount: allSnapshot.size,
            foundCount: documentsToDelete.length,
            scanProgress: (scannedCount / allSnapshot.size) * 100,
            currentDocument: data.name || `Document ${scannedCount}`
          });
        }
      } catch (docError) {
        console.error('❌ Error processing document during scan:', docError);
      }
    });

    console.log(`✅ Found ${documentsToDelete.length} measurements to delete for year ${year}`);

    if (documentsToDelete.length === 0) {
      console.log(`ℹ️ No measurements found for year ${year}`);
      endTimer('bulk_delete_performance');
      return { deletedCount: 0, errors: [] };
    }

    // Delete documents in batches (Firestore batch limit is 500 operations)
    const batchSize = 500;
    const batches = [];
    let deletedCount = 0;
    const errors = [];

    for (let i = 0; i < documentsToDelete.length; i += batchSize) {
      const batchDocs = documentsToDelete.slice(i, i + batchSize);
      batches.push(batchDocs);
    }

    console.log(`🔄 Deleting ${documentsToDelete.length} documents in ${batches.length} batches...`);

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      try {
        const batch = writeBatch(db);
        const batchDocs = batches[batchIndex];

        // Add delete operations to batch
        batchDocs.forEach((docInfo) => {
          const docRef = doc(db, MEASUREMENTS_COLLECTION, docInfo.id);
          batch.delete(docRef);
        });

        // Commit the batch
        await batch.commit();
        deletedCount += batchDocs.length;

        console.log(`✅ Batch ${batchIndex + 1}/${batches.length} completed: ${batchDocs.length} documents deleted`);

        // Report detailed progress
        if (onProgress) {
          const progressPercent = (deletedCount / documentsToDelete.length) * 100;
          const currentBatchInfo = batchDocs.map(doc => doc.name).join(', ');

          onProgress({
            phase: 'deleting',
            deletedCount,
            totalToDelete: documentsToDelete.length,
            batchIndex: batchIndex + 1,
            totalBatches: batches.length,
            deleteProgress: progressPercent,
            currentBatch: `Batch ${batchIndex + 1}: ${currentBatchInfo.substring(0, 50)}${currentBatchInfo.length > 50 ? '...' : ''}`,
            batchSize: batchDocs.length,
            remainingBatches: batches.length - (batchIndex + 1)
          });
        }

      } catch (batchError) {
        console.error(`❌ Error in batch ${batchIndex + 1}:`, batchError);
        errors.push({
          batchIndex: batchIndex + 1,
          error: batchError.message,
          documentsInBatch: batches[batchIndex].length
        });
      }
    }

    // Clear search cache since data was deleted
    clearSearchCache();

    const result = { deletedCount, errors };
    console.log(`🎉 Bulk delete completed for year ${year}:`, result);
    endTimer('bulk_delete_performance');

    return result;

  } catch (error) {
    console.error(`❌ Error in bulk delete for year ${year}:`, error);
    endTimer('bulk_delete_performance');
    throw error;
  }
};

/**
 * Get available years from the database (only years with actual data)
 * @returns {Promise<number[]>} Array of available years that have measurements
 */
export const getAvailableYears = async () => {
  try {
    console.log('🔍 Getting available years from database...');
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Get all measurements to analyze years accurately
    const testQuery = query(
      measurementsRef,
      orderBy('timestamp', 'desc')
    );

    const querySnapshot = await getDocs(testQuery);
    console.log('📊 Analyzing', querySnapshot.size, 'measurements for year distribution...');

    const yearCounts = {};

    querySnapshot.forEach((doc) => {
      try {
        const data = doc.data();

        // Extract year from date field
        if (data.date) {
          let year;
          if (data.date.includes('-')) {
            const parts = data.date.split('-');
            if (parts[0].length === 4) {
              // YYYY-MM-DD format
              year = parseInt(parts[0]);
            } else {
              // DD-MM-YYYY format
              year = parseInt(parts[2]);
            }
          } else {
            // Try to parse as date
            const parsedDate = new Date(data.date);
            year = parsedDate.getFullYear();
          }

          if (year && year > 1900 && year < 2100) {
            yearCounts[year] = (yearCounts[year] || 0) + 1;
          }
        }
      } catch (error) {
        console.error('Error processing document for year extraction:', error);
      }
    });

    // Sort years and return only years that have data
    const availableYears = Object.keys(yearCounts)
      .map(year => parseInt(year))
      .filter(year => yearCounts[year] > 0) // Only include years with data
      .sort((a, b) => b - a); // Sort descending (newest first)

    console.log('✅ Available years with data:', availableYears);
    console.log('📊 Year distribution:', yearCounts);

    return availableYears;

  } catch (error) {
    console.error('❌ Error getting available years:', error);
    return []; // Return empty array if error - no fallback data
  }
};

/**
 * Subscribe to measurements with pagination and load more functionality
 * @param {Function} callback - Function to call with updated measurements and pagination info
 * @param {number} pageSize - Number of measurements to load per page
 * @param {Function} onError - Function to call on error
 * @returns {{ unsubscribe: Function, loadMore: Function }} Object containing unsubscribe and loadMore functions
 */
export const subscribeMeasurements = (callback, pageSize = 20, onError = () => {}) => {
  console.log('🔄 Setting up real-time measurements subscription with pagination...');

  // Start performance monitoring
  startTimer('measurements_initial_load');

  // Track if subscription is active to prevent duplicate calls
  let isActive = true;

  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);

    // Helper function to parse date and create timestamp for sorting
    const createTimestamp = (dateStr) => {
      if (!dateStr) return Date.now();
      let parsedDate;
      if (dateStr.includes('-')) {
        const parts = dateStr.split('-');
        if (parts[0].length === 4) {
          // YYYY-MM-DD format
          parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
        } else {
          // DD-MM-YYYY format
          parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }
      } else {
        parsedDate = new Date(dateStr);
      }
      return parsedDate.getTime();
    };

    // Pagination state
    let loadedMeasurements = [];
    let lastDocument = null;
    let isLoading = false;
    let allLoaded = false;
    let realtimeUnsubscribe = null;

    // Helper function to load a page of measurements
    const loadPage = async (startAfterDoc = null) => {
      console.log(`📄 Loading page of ${pageSize} measurements...`);

      try {
        // Build query with proper sorting by timestamp (latest first)
        let pageQuery;
        if (startAfterDoc) {
          pageQuery = query(
            measurementsRef,
            orderBy('timestamp', 'desc'),
            startAfter(startAfterDoc),
            limit(pageSize)
          );
        } else {
          pageQuery = query(
            measurementsRef,
            orderBy('timestamp', 'desc'),
            limit(pageSize)
          );
        }

        const pageSnapshot = await getDocs(pageQuery);
        console.log(`✅ Page query successful: ${pageSnapshot.size} measurements`);

        const pageMeasurements = [];
        pageSnapshot.forEach((doc) => {
          try {
            const data = doc.data();

            // Create measurement object with all required fields
            const measurement = {
              id: doc.id,
              name: data.name || TEXT_CONSTANTS.DEFAULT_CUSTOMER_NAME,
              phoneNo: data.phoneNo || data.phone || '',
              billNo: data.billNo || data.billNumber || '',
              date: data.date || new Date().toISOString().split('T')[0],
              deliveryDate: data.deliveryDate || '',
              imageUrl: data.imageUrl || '',
              measurements: data.measurements || {},
              notes: data.notes || '',
              timestamp: data.timestamp || Date.now(),
              deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date)
            };

            pageMeasurements.push(measurement);
          } catch (docError) {
            console.error('❌ Error processing document:', docError);
          }
        });

        return {
          measurements: pageMeasurements,
          lastDoc: pageSnapshot.docs[pageSnapshot.docs.length - 1] || null,
          hasMore: pageSnapshot.size === pageSize
        };

      } catch (error) {
        console.error(`❌ Error loading page:`, error);
        return {
          measurements: [],
          lastDoc: null,
          hasMore: false
        };
      }
    };

    // Real-time subscription function
    const setupRealtimeSubscription = () => {
      try {
        console.log('🔄 Setting up real-time subscription...');

        // Create query for real-time subscription (first page only)
        const realtimeQuery = query(
          measurementsRef,
          orderBy('timestamp', 'desc'),
          limit(pageSize)
        );

        // Set up real-time listener
        realtimeUnsubscribe = onSnapshot(
          realtimeQuery,
          (snapshot) => {
            try {
              console.log(`📡 Real-time update received: ${snapshot.size} documents`);

              const realtimeMeasurements = [];
              snapshot.forEach((doc) => {
                try {
                  const data = doc.data();

                  // Create measurement object with all required fields
                  const measurement = {
                    id: doc.id,
                    name: data.name || TEXT_CONSTANTS.DEFAULT_CUSTOMER_NAME,
                    phoneNo: data.phoneNo || data.phone || '',
                    billNo: data.billNo || data.billNumber || '',
                    date: data.date || new Date().toISOString().split('T')[0],
                    deliveryDate: data.deliveryDate || '',
                    imageUrl: data.imageUrl || '',
                    measurements: data.measurements || {},
                    notes: data.notes || '',
                    timestamp: data.timestamp || Date.now(),
                    deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date)
                  };

                  realtimeMeasurements.push(measurement);
                } catch (docError) {
                  console.error('❌ Error processing document in real-time update:', docError);
                }
              });

              // Update loaded measurements with real-time data
              loadedMeasurements = realtimeMeasurements;
              lastDocument = snapshot.docs[snapshot.docs.length - 1] || null;
              allLoaded = snapshot.size < pageSize;

              endTimer('measurements_initial_load');
              console.log(`✅ Real-time update processed: ${loadedMeasurements.length} measurements, hasMore: ${!allLoaded}`);

              // Only call callback if subscription is still active
              if (isActive) {
                callback(loadedMeasurements, { isLoading: false, allLoaded, hasMore: !allLoaded });
              }

            } catch (error) {
              console.error('❌ Error processing real-time update:', error);
              onError(error);
            }
          },
          (error) => {
            console.error('❌ Real-time subscription error:', error);
            if (isActive) {
              onError(error);
              callback([], { isLoading: false, allLoaded: true, hasMore: false });
            }
          }
        );

      } catch (error) {
        console.error('❌ Error setting up real-time subscription:', error);
        onError(error);
        callback([], { isLoading: false, allLoaded: true, hasMore: false });
      }
    };

    // LoadMore function for pagination
    const loadMore = async () => {
      if (isLoading || allLoaded) {
        console.log('🚫 Cannot load more:', { isLoading, allLoaded, hasLastDoc: !!lastDocument });
        return;
      }

      if (!lastDocument) {
        console.log('🚫 No lastDocument available for pagination');
        allLoaded = true;
        callback(loadedMeasurements, { isLoading: false, allLoaded: true, hasMore: false });
        return;
      }

      try {
        isLoading = true;
        callback(loadedMeasurements, { isLoading: true, allLoaded, hasMore: !allLoaded });

        console.log('🔄 Loading next page...');
        const pageResult = await loadPage(lastDocument);

        // Append new data
        const newMeasurements = [...loadedMeasurements, ...pageResult.measurements];
        loadedMeasurements = newMeasurements;
        lastDocument = pageResult.lastDoc;
        allLoaded = !pageResult.hasMore;

        console.log(`✅ Loaded ${pageResult.measurements.length} more measurements. Total: ${loadedMeasurements.length}, hasMore: ${pageResult.hasMore}`);
        callback(loadedMeasurements, { isLoading: false, allLoaded, hasMore: pageResult.hasMore });

      } catch (error) {
        console.error('❌ Error in loadMore:', error);
        onError(error);
      } finally {
        isLoading = false;
      }
    };

    // Start real-time subscription
    setupRealtimeSubscription();

    // Return the functions
    return {
      unsubscribe: () => {
        console.log('🔄 Unsubscribing from measurements');
        isActive = false;
        if (realtimeUnsubscribe) {
          realtimeUnsubscribe();
          realtimeUnsubscribe = null;
        }
      },
      loadMore
    };

  } catch (error) {
    console.error('❌ Error setting up measurements listener:', error);
    onError(error);
    // Return no-op function in case of error
    return {
      unsubscribe: () => {}
    };
  }
};

/**
 * Get the total count of measurements in the database
 * @returns {Promise<number>} Total count of measurements
 */
export const getTotalMeasurementsCount = async () => {
  try {
    console.log('🔢 Getting total measurements count...');
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const snapshot = await getCountFromServer(measurementsRef);
    const count = snapshot.data().count;
    console.log(`✅ Total measurements in database: ${count}`);
    return count;
  } catch (error) {
    console.error('❌ Error getting measurements count:', error);
    return 0;
  }
};
