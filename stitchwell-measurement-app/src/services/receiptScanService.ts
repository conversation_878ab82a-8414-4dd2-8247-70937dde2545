import * as FileSystem from 'expo-file-system';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Google AI configuration with multiple models for fallback
const GOOGLE_AI_MODELS = [
  {
    name: 'gemini-2.0-flash-exp',
    description: 'Gemini 2.0 Flash Experimental (Primary)',
    priority: 1,
    supportsVision: true
  },
  {
    name: 'gemini-1.5-flash',
    description: 'Gemini 1.5 Flash',
    priority: 2,
    supportsVision: true
  },
  {
    name: 'gemini-1.5-pro',
    description: 'Gemini 1.5 Pro',
    priority: 3,
    supportsVision: true
  },
  {
    name: 'gemini-1.0-pro-vision',
    description: 'Gemini 1.0 Pro Vision',
    priority: 4,
    supportsVision: true
  }
];

// Get friendly model name for display
export const getModelDisplayName = (modelName: string): string => {
  const model = GOOGLE_AI_MODELS.find(m => m.name === modelName);
  if (model) {
    return model.description;
  }

  // Fallback to extracting a readable name from the model string
  return modelName.charAt(0).toUpperCase() + modelName.slice(1).replace(/-/g, ' ');
};

// Get model statistics for debugging/info
export const getModelStats = () => {
  const visionModels = GOOGLE_AI_MODELS.filter(m => m.supportsVision);
  const textModels = GOOGLE_AI_MODELS.filter(m => !m.supportsVision);

  return {
    total: GOOGLE_AI_MODELS.length,
    vision: visionModels.length,
    text: textModels.length,
    models: GOOGLE_AI_MODELS.map(m => ({
      name: m.description,
      type: 'Vision', // All Google AI models support vision
      priority: m.priority
    }))
  };
};

// Get Google AI API key from environment
const getGoogleAIKey = () => {
  // In production, this should come from secure storage or environment variables
  return process.env.GOOGLE_AI_MODEL_API_KEY || 'AIzaSyBV5_is2kZxHjcx68z9l8ZNkkw899ftulE';
};

export interface ExtractedReceiptData {
  customerName?: string;
  phoneNumber?: string;
  billNumber?: string;
  amount?: string;
  billDate?: string; // Date when the bill was created
  deliveryDate?: string; // Date when the order should be delivered
  confidence: number;
  extractedText?: string;
  imageUri?: string; // Add image URI to pass the scanned image
}

export interface ScanResult {
  success: boolean;
  data?: ExtractedReceiptData;
  error?: string;
  modelUsed?: string;
}

// Convert image to base64 for Google AI
const convertImageToBase64 = async (imageUri: string): Promise<string> => {
  try {
    const base64 = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    return base64;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw new Error('Failed to process image');
  }
};

// Initialize Google AI client
const initializeGoogleAI = () => {
  const apiKey = getGoogleAIKey();
  if (!apiKey) {
    throw new Error('Google AI API key not configured');
  }
  return new GoogleGenerativeAI(apiKey);
};

// Create the prompt for receipt scanning
const createReceiptScanPrompt = (): string => {
  return `You are an expert at extracting information from handwritten tailor receipts.

Analyze this receipt image and extract the following information:
- Customer Name
- Phone Number
- Bill Number
- Amount (numerical value only)
- Bill Date (date when the bill was created/written - in DD-MM-YYYY format)
- Delivery Date (date when the order should be delivered - in DD-MM-YYYY format)

IMPORTANT INSTRUCTIONS:
1. Look for handwritten text carefully
2. Phone numbers may be in various formats (with/without country codes, spaces, dashes)
3. Amounts may have currency symbols (₹, Rs, etc.) - extract only the number
4. Dates may be in various formats - convert to DD-MM-YYYY
5. DATE POSITIONING - VERY IMPORTANT:
   - Bill Date: Look at the TOP of the receipt - this is when the bill was created/written
   - Delivery Date: Look at the BOTTOM of the receipt - this is when the order should be delivered
   - There are typically TWO dates on tailor bills with this specific layout pattern
6. Bill numbers may be alphanumeric
7. If you cannot find a field, return null for that field
8. Extract whatever you can find - partial data is better than no data
9. Provide a confidence score (0-100) for the overall extraction
10. Even if only 1-2 fields are found, still return them
11. If only one date is found:
    - If it's at the top → it's likely the bill date
    - If it's at the bottom → it's likely the delivery date
    - If position is unclear, prioritize delivery date as it's more critical

Return ONLY a valid JSON object with this exact structure:
{
  "customerName": "extracted name or null",
  "phoneNumber": "extracted phone or null",
  "billNumber": "extracted bill number or null",
  "amount": "extracted amount or null",
  "billDate": "extracted bill creation date from TOP of receipt in DD-MM-YYYY or null",
  "deliveryDate": "extracted delivery date from BOTTOM of receipt in DD-MM-YYYY or null",
  "confidence": confidence_score_number,
  "extractedText": "brief summary of what text was visible"
}

IMPORTANT: Return ONLY the JSON object. Do not wrap it in markdown code blocks. Do not include any other text, explanations, or formatting. Just the pure JSON object.`;
};

// Call Google AI API
const callGoogleAI = async (
  base64Image: string,
  modelName: string
): Promise<ExtractedReceiptData> => {
  const genAI = initializeGoogleAI();
  const model = genAI.getGenerativeModel({ model: modelName });

  const prompt = createReceiptScanPrompt();

  const imagePart = {
    inlineData: {
      data: base64Image,
      mimeType: "image/jpeg"
    }
  };

  const result = await model.generateContent([prompt, imagePart]);
  const response = result.response;
  const content = response.text();

  // Clean the response - remove markdown code blocks if present
  let cleanContent = content.trim();

  // Remove markdown code blocks (like ```json or ```)
  if (cleanContent.startsWith('```json')) {
    cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
  } else if (cleanContent.startsWith('```')) {
    cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
  }

  try {
    // Parse the cleaned JSON response
    const extractedData = JSON.parse(cleanContent);

    // Validate the response structure
    if (typeof extractedData !== 'object') {
      throw new Error('Response is not a valid object');
    }

    // Clean and validate extracted data
    const cleanData = {
      customerName: extractedData.customerName && extractedData.customerName !== 'null' ? extractedData.customerName.trim() : undefined,
      phoneNumber: extractedData.phoneNumber && extractedData.phoneNumber !== 'null' ? extractedData.phoneNumber.trim() : undefined,
      billNumber: extractedData.billNumber && extractedData.billNumber !== 'null' ? extractedData.billNumber.trim() : undefined,
      amount: extractedData.amount && extractedData.amount !== 'null' ? extractedData.amount.toString().trim() : undefined,
      billDate: extractedData.billDate && extractedData.billDate !== 'null' ? extractedData.billDate.trim() : undefined,
      deliveryDate: extractedData.deliveryDate && extractedData.deliveryDate !== 'null' ? extractedData.deliveryDate.trim() : undefined,
      confidence: extractedData.confidence || 0,
      extractedText: extractedData.extractedText || undefined,
    };

    // Check if at least one field was extracted
    const hasAnyData = cleanData.customerName || cleanData.phoneNumber || cleanData.billNumber || cleanData.amount || cleanData.billDate || cleanData.deliveryDate;

    if (!hasAnyData) {
      throw new Error('No usable data could be extracted from the receipt');
    }

    return cleanData;
  } catch (parseError) {
    console.error('Error parsing Google AI response:', parseError);
    console.error('Raw content:', content);
    console.error('Cleaned content:', cleanContent);
    throw new Error('Failed to parse AI response');
  }
};

// Main function to scan receipt with Google AI model fallbacks
export const scanReceipt = async (imageUri: string): Promise<ScanResult> => {
  try {
    console.log('🔍 Starting receipt scan for image:', imageUri);

    // Convert image to base64
    const base64Image = await convertImageToBase64(imageUri);
    console.log('✅ Image converted to base64, size:', base64Image.length);

    let lastError: Error | null = null;

    console.log(`📋 Will try ${GOOGLE_AI_MODELS.length} Google AI models`);

    // Try each Google AI model in order of priority
    for (let i = 0; i < GOOGLE_AI_MODELS.length; i++) {
      const model = GOOGLE_AI_MODELS[i];

      try {
        console.log(`🤖 Trying model ${i + 1}/${GOOGLE_AI_MODELS.length}: ${model.description} (${model.name})`);
        const data = await callGoogleAI(base64Image, model.name);
        console.log(`✅ Model ${i + 1} successful! Confidence: ${data.confidence}%`);

        return {
          success: true,
          data,
          modelUsed: model.name,
        };
      } catch (error) {
        console.warn(`⚠️ Model ${i + 1} (${model.description}) failed:`, error);
        lastError = error as Error;

        // If this isn't the last model, continue to next one
        if (i < GOOGLE_AI_MODELS.length - 1) {
          console.log(`🔄 Trying next model...`);
          continue;
        }
      }
    }

    // All models failed
    console.error('❌ All Google AI models failed');
    return {
      success: false,
      error: `All ${GOOGLE_AI_MODELS.length} Google AI models failed. Last error: ${lastError?.message || 'Unknown error'}`,
    };

  } catch (error) {
    console.error('❌ Receipt scan failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Utility function to validate extracted data
export const validateExtractedData = (data: ExtractedReceiptData): string[] => {
  const issues: string[] = [];

  // Only validate if data exists - don't complain about missing fields
  if (data.phoneNumber && data.phoneNumber.trim()) {
    const cleanPhone = data.phoneNumber.replace(/\s/g, '');
    if (!/^[\d\-\+\(\)]{7,15}$/.test(cleanPhone)) {
      issues.push('phone number format');
    }
  }

  if (data.amount && data.amount.trim()) {
    if (isNaN(parseFloat(data.amount))) {
      issues.push('amount value');
    }
  }

  if (data.billDate && data.billDate.trim()) {
    if (!/^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}$/.test(data.billDate)) {
      issues.push('bill date format');
    }
  }

  if (data.deliveryDate && data.deliveryDate.trim()) {
    if (!/^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}$/.test(data.deliveryDate)) {
      issues.push('delivery date format');
    }
  }

  // Only warn about low confidence if it's very low
  if (data.confidence < 30) {
    issues.push('very low confidence');
  }

  return issues;
};
