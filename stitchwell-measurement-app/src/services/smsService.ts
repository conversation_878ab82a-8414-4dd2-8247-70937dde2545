import { Linking, Alert } from 'react-native';
import { Measurement } from '../types';
import { TEXT_CONSTANTS } from '../constants';

export interface SmsTemplate {
  id: string;
  name: string;
  message: string;
  description: string;
}

export interface SmsRecipient {
  id: string;
  name: string;
  phoneNo: string;
  billNo?: string;
  deliveryDate?: string;
}

// Pre-defined SMS templates
export const SMS_TEMPLATES: SmsTemplate[] = [
  {
    id: 'delivery',
    name: 'Delivery Reminder',
    message: TEXT_CONSTANTS.SMS_TEMPLATE_DELIVERY,
    description: 'Notify customers about delivery readiness'
  },
  {
    id: 'payment',
    name: 'Payment Reminder',
    message: TEXT_CONSTANTS.SMS_TEMPLATE_PAYMENT,
    description: 'Send payment reminders to customers'
  },
  {
    id: 'followup',
    name: 'Follow-up',
    message: TEXT_CONSTANTS.SMS_TEMPLATE_FOLLOWUP,
    description: 'Thank customers and follow up on satisfaction'
  }
];

/**
 * Validate phone number format
 */
export const validatePhoneNumber = (phoneNo: string): boolean => {
  if (!phoneNo || phoneNo.trim() === '') {
    return false;
  }
  
  // Remove all non-digit characters
  const cleanPhone = phoneNo.replace(/\D/g, '');
  
  // Check if it has at least 10 digits (minimum for most countries)
  return cleanPhone.length >= 10;
};

/**
 * Format phone number for SMS
 */
export const formatPhoneForSms = (phoneNo: string): string => {
  // Remove all non-digit characters except +
  let cleanPhone = phoneNo.replace(/[^\d+]/g, '');

  // If it doesn't start with +, add country code (assuming India +91)
  if (!cleanPhone.startsWith('+')) {
    // If it's a 10-digit number, add +91
    if (cleanPhone.length === 10) {
      cleanPhone = '+91' + cleanPhone;
    }
    // If it's 11 digits starting with 91, add +
    else if (cleanPhone.length === 12 && cleanPhone.startsWith('91')) {
      cleanPhone = '+' + cleanPhone;
    }
  }

  return cleanPhone;
};

/**
 * Normalize phone number for consistent ID generation
 */
export const normalizePhoneForId = (phoneNo: string): string => {
  // Remove all non-digit characters
  const digitsOnly = phoneNo.replace(/\D/g, '');

  // If it's a 10-digit number, assume it's Indian and add 91
  if (digitsOnly.length === 10) {
    return '91' + digitsOnly;
  }

  // If it's 12 digits starting with 91, use as is
  if (digitsOnly.length === 12 && digitsOnly.startsWith('91')) {
    return digitsOnly;
  }

  // For other cases, just return the digits
  return digitsOnly;
};

/**
 * Replace template variables in message
 */
export const replaceTemplateVariables = (
  message: string, 
  customer: SmsRecipient
): string => {
  return message
    .replace(/{name}/g, customer.name || 'Customer')
    .replace(/{billNo}/g, customer.billNo || 'N/A')
    .replace(/{deliveryDate}/g, customer.deliveryDate || 'TBD');
};

/**
 * Convert measurement to SMS recipient
 */
export const measurementToSmsRecipient = (measurement: Measurement): SmsRecipient => {
  if (!measurement) {
    throw new Error('Invalid measurement object provided');
  }

  return {
    id: measurement.id || '',
    name: measurement.name || 'Unknown Customer',
    phoneNo: measurement.phoneNo || '',
    billNo: measurement.billNo || '',
    deliveryDate: measurement.deliveryDate || ''
  };
};

/**
 * Send SMS to a single recipient using device's SMS app
 */
export const sendSmsToRecipient = async (
  recipient: SmsRecipient,
  message: string
): Promise<boolean> => {
  try {
    // Validate phone number
    if (!validatePhoneNumber(recipient.phoneNo)) {
      throw new Error(`Invalid phone number for ${recipient.name}: ${recipient.phoneNo}`);
    }

    // Format phone number
    const formattedPhone = formatPhoneForSms(recipient.phoneNo);
    
    // Replace template variables
    const personalizedMessage = replaceTemplateVariables(message, recipient);
    
    // Create SMS URL
    const smsUrl = `sms:${formattedPhone}?body=${encodeURIComponent(personalizedMessage)}`;
    
    // Check if SMS is supported
    const canOpen = await Linking.canOpenURL(smsUrl);
    if (!canOpen) {
      throw new Error('SMS not supported on this device');
    }
    
    // Open SMS app
    await Linking.openURL(smsUrl);
    return true;
    
  } catch (error) {
    console.error('Error sending SMS to', recipient.name, ':', error);
    throw error;
  }
};

/**
 * Send SMS to multiple recipients (opens SMS app for each)
 */
export const sendBulkSms = async (
  recipients: SmsRecipient[],
  message: string,
  onProgress?: (current: number, total: number, recipient: SmsRecipient) => void
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[]
  };

  for (let i = 0; i < recipients.length; i++) {
    const recipient = recipients[i];
    
    try {
      // Call progress callback
      if (onProgress) {
        onProgress(i + 1, recipients.length, recipient);
      }
      
      await sendSmsToRecipient(recipient, message);
      results.success++;
      
      // Add small delay between SMS sends to avoid overwhelming the system
      if (i < recipients.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
    } catch (error) {
      results.failed++;
      results.errors.push(`${recipient.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
};

/**
 * Show confirmation dialog before sending SMS
 */
export const confirmSendSms = (
  recipients: SmsRecipient[],
  message: string,
  onConfirm: () => void
): void => {
  const recipientCount = recipients.length;
  const recipientNames = recipients.slice(0, 3).map(r => r.name).join(', ');
  const moreText = recipientCount > 3 ? ` and ${recipientCount - 3} more` : '';
  
  Alert.alert(
    'Confirm Send SMS',
    `Send message to ${recipientCount} recipient${recipientCount > 1 ? 's' : ''}?\n\n` +
    `Recipients: ${recipientNames}${moreText}\n\n` +
    `Message preview: "${message.substring(0, 100)}${message.length > 100 ? '...' : ''}"`,
    [
      {
        text: 'Cancel',
        style: 'cancel'
      },
      {
        text: 'Send',
        style: 'default',
        onPress: onConfirm
      }
    ]
  );
};

/**
 * Get unique customers from measurements (deduplicate by phone number)
 */
export const getUniqueCustomers = (measurements: Measurement[]): SmsRecipient[] => {
  if (!measurements || !Array.isArray(measurements)) {
    console.warn('getUniqueCustomers: Invalid measurements array provided');
    return [];
  }

  const uniqueCustomers = new Map<string, SmsRecipient>();

  measurements.forEach(measurement => {
    try {
      const phoneNo = measurement?.phoneNo?.trim();
      if (phoneNo && validatePhoneNumber(phoneNo)) {
        // Normalize phone number for consistent ID generation
        const normalizedPhone = normalizePhoneForId(phoneNo);

        // Use normalized phone number as key to avoid duplicates
        if (!uniqueCustomers.has(normalizedPhone)) {
          const recipient = measurementToSmsRecipient(measurement);
          // Use normalized phone number as ID for consistent selection
          recipient.id = normalizedPhone;
          uniqueCustomers.set(normalizedPhone, recipient);
        }
      }
    } catch (error) {
      console.error('Error processing measurement for SMS:', error, measurement);
    }
  });

  const result = Array.from(uniqueCustomers.values()).sort((a, b) => a.name.localeCompare(b.name));
  console.log(`getUniqueCustomers: Found ${result.length} unique customers with valid phone numbers`);
  return result;
};
