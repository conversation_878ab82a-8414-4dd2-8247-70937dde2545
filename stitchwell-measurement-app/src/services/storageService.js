import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '../../firebaseConfig';
import * as ImageManipulator from 'expo-image-manipulator';

// Upload image to Firebase Storage
export const uploadImage = async (uri) => {
  try {
    // Resize and compress the image while preserving aspect ratio
    const manipResult = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: 1200 } }], // Only specify width to preserve aspect ratio
      { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
    );
    
    // Convert to blob
    const response = await fetch(manipResult.uri);
    const blob = await response.blob();
    
    // Generate unique filename
    const filename = `photo_${Date.now()}.jpg`;
    const storageRef = ref(storage, `photos/${filename}`);
    
    // Upload to Firebase Storage
    await uploadBytes(storageRef, blob);
    
    // Get download URL
    const downloadURL = await getDownloadURL(storageRef);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};