/**
 * Simple performance monitoring utility for tracking loading times
 */

class PerformanceMonitor {
  constructor() {
    this.timers = new Map();
    this.metrics = new Map();
  }

  /**
   * Start timing an operation
   * @param {string} operation - Name of the operation
   */
  startTimer(operation) {
    this.timers.set(operation, Date.now());
    console.log(`⏱️ Started timing: ${operation}`);
  }

  /**
   * End timing an operation and log the result
   * @param {string} operation - Name of the operation
   * @returns {number} Duration in milliseconds
   */
  endTimer(operation) {
    const startTime = this.timers.get(operation);
    if (!startTime) {
      console.warn(`⚠️ No timer found for operation: ${operation}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(operation);
    
    // Store metric
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    this.metrics.get(operation).push(duration);

    // Log result with performance indicator
    const performanceIndicator = this.getPerformanceIndicator(duration);
    console.log(`⏱️ ${performanceIndicator} ${operation}: ${duration}ms`);
    
    return duration;
  }

  /**
   * Get performance indicator emoji based on duration
   * @param {number} duration - Duration in milliseconds
   * @returns {string} Performance indicator
   */
  getPerformanceIndicator(duration) {
    if (duration < 1000) return '🚀'; // Very fast
    if (duration < 3000) return '✅'; // Good
    if (duration < 5000) return '⚠️'; // Slow
    return '🐌'; // Very slow
  }

  /**
   * Get average time for an operation
   * @param {string} operation - Name of the operation
   * @returns {number} Average duration in milliseconds
   */
  getAverageTime(operation) {
    const times = this.metrics.get(operation);
    if (!times || times.length === 0) return 0;
    
    const sum = times.reduce((acc, time) => acc + time, 0);
    return Math.round(sum / times.length);
  }

  /**
   * Get performance summary for all operations
   * @returns {Object} Performance summary
   */
  getSummary() {
    const summary = {};
    for (const [operation, times] of this.metrics.entries()) {
      if (times.length > 0) {
        const avg = this.getAverageTime(operation);
        const min = Math.min(...times);
        const max = Math.max(...times);
        summary[operation] = {
          count: times.length,
          average: avg,
          min,
          max,
          indicator: this.getPerformanceIndicator(avg)
        };
      }
    }
    return summary;
  }

  /**
   * Log performance summary
   */
  logSummary() {
    const summary = this.getSummary();
    console.log('📊 Performance Summary:');
    for (const [operation, stats] of Object.entries(summary)) {
      console.log(`  ${stats.indicator} ${operation}: avg ${stats.average}ms (${stats.count} calls, min: ${stats.min}ms, max: ${stats.max}ms)`);
    }
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.timers.clear();
    this.metrics.clear();
    console.log('🧹 Performance metrics cleared');
  }
}

// Create a singleton instance
const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;

// Convenience functions
export const startTimer = (operation) => performanceMonitor.startTimer(operation);
export const endTimer = (operation) => performanceMonitor.endTimer(operation);
export const getAverageTime = (operation) => performanceMonitor.getAverageTime(operation);
export const logSummary = () => performanceMonitor.logSummary();
export const clearMetrics = () => performanceMonitor.clear();
