// Test script to check what years actually have data
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, where, orderBy, limit, startAfter } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Test what years have data
async function testAvailableYears() {
  try {
    console.log('🧪 Testing available years in database...\n');

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Get sample measurements to analyze years
    console.log('📊 Fetching sample measurements to analyze years...');
    const sampleQuery = query(measurementsRef, orderBy('timestamp', 'desc'), limit(100));
    const sampleSnapshot = await getDocs(sampleQuery);
    console.log(`📋 Sample measurements retrieved: ${sampleSnapshot.size}`);

    // Analyze years
    const yearCounts = {};

    sampleSnapshot.forEach((doc) => {
      const data = doc.data();
      const timestamp = data.timestamp;
      
      if (timestamp) {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        
        if (!yearCounts[year]) {
          yearCounts[year] = 0;
        }
        yearCounts[year]++;
      }
    });

    // Sort years
    const availableYears = Object.keys(yearCounts).map(Number).sort((a, b) => b - a);
    
    console.log('\n📅 Years found in sample data (newest first):');
    availableYears.forEach(year => {
      console.log(`   ${year}: ${yearCounts[year]} measurements`);
    });

    // Test simple pagination without year filtering
    console.log('\n🔄 Testing simple pagination (no year filtering)...');
    
    try {
      const page1Query = query(
        measurementsRef,
        orderBy('timestamp', 'desc'),
        limit(15)
      );
      
      const page1Snapshot = await getDocs(page1Query);
      console.log(`✅ Page 1 query successful: ${page1Snapshot.size} measurements`);
      
      if (page1Snapshot.size > 0) {
        console.log('📝 Page 1 measurements:');
        page1Snapshot.docs.slice(0, 5).forEach((doc, idx) => {
          const data = doc.data();
          const date = new Date(data.timestamp);
          console.log(`   ${idx + 1}. ${data.name || 'Unknown'} - ${date.toLocaleDateString()}`);
        });

        // Test getting next page
        const lastDoc = page1Snapshot.docs[page1Snapshot.docs.length - 1];
        console.log('\n🔄 Testing page 2...');
        
        const page2Query = query(
          measurementsRef,
          orderBy('timestamp', 'desc'),
          startAfter(lastDoc),
          limit(15)
        );
        
        const page2Snapshot = await getDocs(page2Query);
        console.log(`✅ Page 2 query successful: ${page2Snapshot.size} measurements`);
        
        if (page2Snapshot.size > 0) {
          console.log('📝 Page 2 measurements:');
          page2Snapshot.docs.slice(0, 3).forEach((doc, idx) => {
            const data = doc.data();
            const date = new Date(data.timestamp);
            console.log(`   ${idx + 1}. ${data.name || 'Unknown'} - ${date.toLocaleDateString()}`);
          });
        }
      }
      
    } catch (error) {
      console.log(`❌ Simple query failed: ${error.message}`);
    }

    console.log('\n🎉 Available years test completed!');
    console.log('\n📋 Summary:');
    console.log(`   Sample size: ${sampleSnapshot.size}`);
    console.log(`   Years found: ${availableYears.join(', ')}`);
    if (availableYears.length > 0) {
      console.log(`   Most recent year: ${availableYears[0]} (${yearCounts[availableYears[0]]} in sample)`);
    }

  } catch (error) {
    console.error('❌ Available years test failed:', error);
  }
}

// Run the test
testAvailableYears().then(() => {
  console.log('🏁 Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
