// Test script for bulk delete functionality
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, getDocs, query, where } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

/**
 * Create test data for 2020 to test bulk delete
 */
const createTestData2020 = async () => {
  try {
    console.log('🔄 Creating test data for 2020...');
    
    const testRecords = [
      {
        name: 'Test Customer 1',
        phoneNo: '1234567890',
        billNo: 'TEST2020001',
        date: '15-01-2020',
        deliveryDate: '20-01-2020',
        imageUrl: '',
        measurements: { chest: 40, waist: 32 },
        notes: 'Test record for 2020 - should be deleted',
        timestamp: new Date('2020-01-15').getTime()
      },
      {
        name: 'Test Customer 2',
        phoneNo: '1234567891',
        billNo: 'TEST2020002',
        date: '10-06-2020',
        deliveryDate: '15-06-2020',
        imageUrl: '',
        measurements: { chest: 42, waist: 34 },
        notes: 'Another test record for 2020 - should be deleted',
        timestamp: new Date('2020-06-10').getTime()
      },
      {
        name: 'Test Customer 3',
        phoneNo: '1234567892',
        billNo: 'TEST2020003',
        date: '25-12-2020',
        deliveryDate: '30-12-2020',
        imageUrl: '',
        measurements: { chest: 38, waist: 30 },
        notes: 'Last test record for 2020 - should be deleted',
        timestamp: new Date('2020-12-25').getTime()
      },
      {
        name: 'Test Customer 2021',
        phoneNo: '1234567893',
        billNo: 'TEST2021001',
        date: '05-01-2021',
        deliveryDate: '10-01-2021',
        imageUrl: '',
        measurements: { chest: 44, waist: 36 },
        notes: 'Test record for 2021 - should NOT be deleted',
        timestamp: new Date('2021-01-05').getTime()
      }
    ];

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    for (const record of testRecords) {
      const docRef = await addDoc(measurementsRef, record);
      console.log(`✅ Created test record: ${record.name} (${record.deliveryDate}) - ID: ${docRef.id}`);
    }
    
    console.log(`🎉 Successfully created ${testRecords.length} test records`);
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  }
};

/**
 * Count records by year for verification
 */
const countRecordsByYear = async (year) => {
  try {
    console.log(`🔍 Counting records for year ${year}...`);
    
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const snapshot = await getDocs(measurementsRef);
    
    let count = 0;
    const records = [];
    
    snapshot.forEach((doc) => {
      const data = doc.data();
      const deliveryDate = data.deliveryDate;
      
      if (deliveryDate) {
        let parsedYear;
        if (deliveryDate.includes('-')) {
          const parts = deliveryDate.split('-');
          if (parts[0].length === 4) {
            parsedYear = parseInt(parts[0]);
          } else {
            parsedYear = parseInt(parts[2]);
          }
        }
        
        if (parsedYear === year) {
          count++;
          records.push({
            id: doc.id,
            name: data.name,
            deliveryDate: data.deliveryDate,
            billNo: data.billNo
          });
        }
      }
    });
    
    console.log(`📊 Found ${count} records for year ${year}:`);
    records.forEach(record => {
      console.log(`  - ${record.name} (${record.billNo}) - ${record.deliveryDate}`);
    });
    
    return { count, records };
    
  } catch (error) {
    console.error(`❌ Error counting records for year ${year}:`, error);
    return { count: 0, records: [] };
  }
};

/**
 * Test the bulk delete functionality
 */
const testBulkDelete = async () => {
  try {
    console.log('🧪 Testing bulk delete functionality...\n');
    
    // Step 1: Create test data
    await createTestData2020();
    console.log('\n');
    
    // Step 2: Count records before deletion
    console.log('📊 BEFORE DELETION:');
    const before2020 = await countRecordsByYear(2020);
    const before2021 = await countRecordsByYear(2021);
    console.log(`2020 records: ${before2020.count}`);
    console.log(`2021 records: ${before2021.count}`);
    console.log('\n');
    
    // Step 3: Import and test the delete function
    const { deleteMeasurementsByYear } = await import('./src/services/measurementService.js');
    
    console.log('🗑️ PERFORMING BULK DELETE FOR 2020...');
    const result = await deleteMeasurementsByYear(2020, (progress) => {
      if (progress.phase === 'scanning') {
        console.log(`📄 Scanning: ${progress.scannedCount}/${progress.totalCount} (found: ${progress.foundCount})`);
      } else if (progress.phase === 'deleting') {
        console.log(`🗑️ Deleting: ${progress.deletedCount}/${progress.totalToDelete} (batch: ${progress.batchIndex}/${progress.totalBatches})`);
      }
    });
    
    console.log('\n✅ BULK DELETE COMPLETED:');
    console.log(`Deleted: ${result.deletedCount} records`);
    console.log(`Errors: ${result.errors.length}`);
    if (result.errors.length > 0) {
      console.log('Errors:', result.errors);
    }
    console.log('\n');
    
    // Step 4: Count records after deletion
    console.log('📊 AFTER DELETION:');
    const after2020 = await countRecordsByYear(2020);
    const after2021 = await countRecordsByYear(2021);
    console.log(`2020 records: ${after2020.count} (should be 0)`);
    console.log(`2021 records: ${after2021.count} (should be unchanged)`);
    
    // Step 5: Verify results
    console.log('\n🔍 VERIFICATION:');
    if (after2020.count === 0) {
      console.log('✅ SUCCESS: All 2020 records were deleted');
    } else {
      console.log(`❌ FAILURE: ${after2020.count} 2020 records still exist`);
    }
    
    if (after2021.count === before2021.count) {
      console.log('✅ SUCCESS: 2021 records were preserved');
    } else {
      console.log(`❌ FAILURE: 2021 records changed from ${before2021.count} to ${after2021.count}`);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
};

// Run the test
testBulkDelete()
  .then(() => {
    console.log('\n🏁 Test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
