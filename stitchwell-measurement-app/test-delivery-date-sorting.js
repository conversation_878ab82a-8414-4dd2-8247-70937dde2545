// Test script to verify delivery date sorting functionality
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, orderBy, limit } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Helper function to create timestamp from date string
const createTimestamp = (dateStr) => {
  if (!dateStr) return Date.now();
  let parsedDate;
  if (dateStr.includes('-')) {
    const parts = dateStr.split('-');
    if (parts[0].length === 4) {
      parsedDate = new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]));
    } else {
      parsedDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
    }
  } else {
    parsedDate = new Date(dateStr);
  }
  return parsedDate.getTime();
};

// Test delivery date sorting
async function testDeliveryDateSorting() {
  try {
    console.log('🧪 Testing delivery date sorting functionality...\n');

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Get sample measurements
    const q = query(measurementsRef, limit(20));
    const querySnapshot = await getDocs(q);
    
    console.log(`📊 Retrieved ${querySnapshot.size} measurements for testing`);
    
    // Process measurements and add delivery timestamps
    const measurements = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const measurement = {
        id: doc.id,
        name: data.name || 'Unknown Customer',
        date: data.date || '',
        deliveryDate: data.deliveryDate || '',
        timestamp: data.timestamp || createTimestamp(data.date),
        deliveryTimestamp: data.deliveryTimestamp || createTimestamp(data.deliveryDate || data.date)
      };
      measurements.push(measurement);
    });

    console.log('\n📋 Sample measurements before sorting:');
    measurements.slice(0, 5).forEach((m, idx) => {
      console.log(`${idx + 1}. ${m.name} - Created: ${m.date}, Delivery: ${m.deliveryDate || 'Not set'}`);
    });

    // Test 1: Sort by delivery date priority (deliveryTimestamp first, then timestamp)
    console.log('\n🔄 Testing delivery date priority sorting...');
    const sortedByDelivery = [...measurements].sort((a, b) => {
      const deliveryTimestampA = a.deliveryTimestamp || a.timestamp || 0;
      const deliveryTimestampB = b.deliveryTimestamp || b.timestamp || 0;
      return deliveryTimestampB - deliveryTimestampA;
    });

    console.log('\n✅ Measurements sorted by delivery date priority:');
    sortedByDelivery.slice(0, 8).forEach((m, idx) => {
      const deliveryDateStr = m.deliveryDate || 'No delivery date';
      const prioritySource = m.deliveryDate ? 'delivery date' : 'creation date';
      console.log(`${idx + 1}. ${m.name} - ${deliveryDateStr} (sorted by ${prioritySource})`);
    });

    // Test 2: Categorize by delivery status
    console.log('\n📅 Testing delivery status categorization...');
    const today = new Date();
    const currentMonth = new Date(2025, 4, 1); // May 2025
    const currentMonthEnd = new Date(2025, 4, 31, 23, 59, 59);

    const categorized = {
      currentMonth: [],
      future: [],
      overdue: [],
      noDeliveryDate: []
    };

    measurements.forEach(m => {
      if (!m.deliveryDate) {
        categorized.noDeliveryDate.push(m);
        return;
      }

      const deliveryDate = new Date(createTimestamp(m.deliveryDate));
      
      if (deliveryDate >= currentMonth && deliveryDate <= currentMonthEnd) {
        categorized.currentMonth.push(m);
      } else if (deliveryDate > currentMonthEnd) {
        categorized.future.push(m);
      } else {
        categorized.overdue.push(m);
      }
    });

    console.log(`📊 Delivery status breakdown:`);
    console.log(`   Current month (May 2025): ${categorized.currentMonth.length}`);
    console.log(`   Future deliveries: ${categorized.future.length}`);
    console.log(`   Overdue deliveries: ${categorized.overdue.length}`);
    console.log(`   No delivery date: ${categorized.noDeliveryDate.length}`);

    // Test 3: Smart priority sorting (like in HomeScreen)
    console.log('\n🎯 Testing smart priority sorting...');
    const smartSorted = [...measurements].sort((a, b) => {
      const parseDate = (dateStr) => {
        if (!dateStr) return null;
        return new Date(createTimestamp(dateStr));
      };

      const deliveryA = parseDate(a.deliveryDate);
      const deliveryB = parseDate(b.deliveryDate);

      const getPriority = (deliveryDate) => {
        if (!deliveryDate) return 4; // No delivery date
        if (deliveryDate >= currentMonth && deliveryDate <= currentMonthEnd) return 1; // Current month
        if (deliveryDate > currentMonthEnd) return 2; // Future
        return 3; // Overdue
      };

      const priorityA = getPriority(deliveryA);
      const priorityB = getPriority(deliveryB);

      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // Within same priority, sort by delivery date
      if (priorityA === 1 || priorityA === 2) {
        if (deliveryA && deliveryB) {
          return deliveryA.getTime() - deliveryB.getTime();
        }
      }

      if (priorityA === 3) {
        if (deliveryA && deliveryB) {
          return deliveryB.getTime() - deliveryA.getTime();
        }
      }

      // Fallback to timestamp
      return (b.timestamp || 0) - (a.timestamp || 0);
    });

    console.log('\n🏆 Smart priority sorted results:');
    smartSorted.slice(0, 8).forEach((m, idx) => {
      const deliveryDate = m.deliveryDate ? new Date(createTimestamp(m.deliveryDate)) : null;
      let status = 'No delivery';
      if (deliveryDate) {
        if (deliveryDate >= currentMonth && deliveryDate <= currentMonthEnd) {
          status = 'Current month';
        } else if (deliveryDate > currentMonthEnd) {
          status = 'Future';
        } else {
          status = 'Overdue';
        }
      }
      console.log(`${idx + 1}. ${m.name} - ${m.deliveryDate || 'No date'} (${status})`);
    });

    console.log('\n🎉 Delivery date sorting test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Delivery date priority sorting implemented');
    console.log('✅ Smart categorization by delivery status working');
    console.log('✅ Fallback to creation timestamp for items without delivery dates');
    console.log('✅ Overdue items sorted by most recent delivery date first');

  } catch (error) {
    console.error('❌ Delivery date sorting test failed:', error);
  }
}

// Run the test
testDeliveryDateSorting().then(() => {
  console.log('🏁 Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
