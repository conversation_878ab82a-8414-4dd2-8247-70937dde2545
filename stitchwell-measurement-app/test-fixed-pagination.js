// Test script to verify the fixed pagination system
import { subscribeMeasurements } from './src/services/measurementService.js';

// Test the fixed pagination
async function testFixedPagination() {
  try {
    console.log('🧪 Testing fixed pagination system...\n');

    let totalReceived = 0;
    let callbackCount = 0;
    let loadMoreFunction = null;

    // Set up subscription with callback
    const { unsubscribe, loadMore } = subscribeMeasurements(
      (measurements, { isLoading, allLoaded }) => {
        callbackCount++;
        totalReceived = measurements.length;
        
        console.log(`📞 Callback ${callbackCount}:`);
        console.log(`   Measurements: ${measurements.length}`);
        console.log(`   Loading: ${isLoading}`);
        console.log(`   All loaded: ${allLoaded}`);
        
        if (!isLoading && measurements.length > 0) {
          console.log(`   📝 Sample measurements:`);
          measurements.slice(0, 3).forEach((m, idx) => {
            console.log(`      ${idx + 1}. ${m.name} - ${m.date} (delivery: ${m.deliveryDate || 'none'})`);
          });
        }
        
        console.log('');
        
        // Store loadMore function for testing
        if (!loadMoreFunction) {
          loadMoreFunction = loadMore;
        }
      },
      10, // Small page size for testing
      (error) => {
        console.error('❌ Subscription error:', error);
      }
    );

    // Wait for initial load
    console.log('⏳ Waiting for initial load...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test loadMore functionality
    if (loadMoreFunction && totalReceived > 0) {
      console.log('🔄 Testing loadMore functionality...');
      
      // Try to load more data 3 times
      for (let i = 1; i <= 3; i++) {
        console.log(`\n📄 LoadMore attempt ${i}:`);
        await loadMoreFunction();
        
        // Wait for the load to complete
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log(`   Current total: ${totalReceived} measurements`);
      }
    }

    // Clean up
    unsubscribe();

    console.log('\n🎉 Fixed pagination test completed!');
    console.log(`📊 Final stats:`);
    console.log(`   Total callbacks: ${callbackCount}`);
    console.log(`   Final measurement count: ${totalReceived}`);

  } catch (error) {
    console.error('❌ Fixed pagination test failed:', error);
  }
}

// Run the test
testFixedPagination().then(() => {
  console.log('🏁 Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
