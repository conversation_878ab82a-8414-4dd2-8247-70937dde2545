// Test script for multi-year bulk delete functionality (2020, 2021, 2022)
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, getDocs } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

/**
 * Create test data for multiple years
 */
const createMultiYearTestData = async () => {
  try {
    console.log('🔄 Creating multi-year test dataset...');
    
    const testRecords = [];
    const years = [2020, 2021, 2022, 2023]; // 2023 should not be deleted
    
    // Create 20 records for each year
    years.forEach(year => {
      for (let i = 1; i <= 20; i++) {
        testRecords.push({
          name: `Test Customer ${year}-${i.toString().padStart(3, '0')}`,
          phoneNo: `${year}${i.toString().padStart(4, '0')}`,
          billNo: `TEST${year}${i.toString().padStart(3, '0')}`,
          date: `${Math.floor(Math.random() * 28) + 1}-${Math.floor(Math.random() * 12) + 1}-${year}`,
          deliveryDate: `${Math.floor(Math.random() * 28) + 1}-${Math.floor(Math.random() * 12) + 1}-${year}`,
          imageUrl: '',
          measurements: { 
            chest: 38 + Math.floor(Math.random() * 10), 
            waist: 30 + Math.floor(Math.random() * 8) 
          },
          notes: `Test record ${i} for ${year} - ${year === 2023 ? 'should NOT be deleted' : 'should be deleted'}`,
          timestamp: new Date(`${year}-${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 28) + 1}`).getTime()
        });
      }
    });

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    console.log(`📝 Creating ${testRecords.length} test records across ${years.length} years...`);
    let created = 0;
    
    for (const record of testRecords) {
      await addDoc(measurementsRef, record);
      created++;
      
      if (created % 20 === 0) {
        console.log(`✅ Created ${created}/${testRecords.length} records`);
      }
    }
    
    console.log(`🎉 Successfully created ${testRecords.length} test records`);
    years.forEach(year => {
      console.log(`   - ${year} records: 20 ${year === 2023 ? '(should be preserved)' : '(will be deleted)'}`);
    });
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  }
};

/**
 * Count records by year for verification
 */
const countRecordsByYear = async (year) => {
  try {
    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    const snapshot = await getDocs(measurementsRef);
    
    let count = 0;
    const records = [];
    
    snapshot.forEach((doc) => {
      const data = doc.data();
      const deliveryDate = data.deliveryDate;
      
      if (deliveryDate) {
        let parsedYear;
        if (deliveryDate.includes('-')) {
          const parts = deliveryDate.split('-');
          if (parts[0].length === 4) {
            parsedYear = parseInt(parts[0]);
          } else {
            parsedYear = parseInt(parts[2]);
          }
        }
        
        if (parsedYear === year) {
          count++;
          records.push({
            id: doc.id,
            name: data.name,
            deliveryDate: data.deliveryDate,
            billNo: data.billNo
          });
        }
      }
    });
    
    return { count, records };
    
  } catch (error) {
    console.error(`❌ Error counting records for year ${year}:`, error);
    return { count: 0, records: [] };
  }
};

/**
 * Test deletion for a specific year
 */
const testYearDeletion = async (year) => {
  try {
    console.log(`\n🗑️ TESTING DELETION FOR YEAR ${year}...`);
    
    // Import the delete function
    const { deleteMeasurementsByYear } = await import('./src/services/measurementService.js');
    
    const startTime = Date.now();
    let progressUpdates = 0;
    
    const result = await deleteMeasurementsByYear(year, (progress) => {
      progressUpdates++;
      const elapsedTime = (Date.now() - startTime) / 1000;
      
      if (progress.phase === 'scanning') {
        const scanPercent = progress.totalCount > 0 ? ((progress.scannedCount / progress.totalCount) * 100).toFixed(1) : 0;
        console.log(`📄 [${elapsedTime.toFixed(1)}s] Scanning: ${progress.scannedCount}/${progress.totalCount} (${scanPercent}%) | Found: ${progress.foundCount}`);
      } else if (progress.phase === 'deleting') {
        const deletePercent = progress.totalToDelete > 0 ? ((progress.deletedCount / progress.totalToDelete) * 100).toFixed(1) : 0;
        const rate = progress.deletedCount / elapsedTime;
        console.log(`🗑️ [${elapsedTime.toFixed(1)}s] Deleting: ${progress.deletedCount}/${progress.totalToDelete} (${deletePercent}%) | Batch: ${progress.batchIndex}/${progress.totalBatches} | Rate: ${rate.toFixed(1)}/s`);
      }
    });
    
    const totalTime = (Date.now() - startTime) / 1000;
    
    console.log(`✅ DELETION COMPLETED FOR ${year}:`);
    console.log(`   Total time: ${totalTime.toFixed(2)} seconds`);
    console.log(`   Deleted records: ${result.deletedCount}`);
    console.log(`   Progress updates: ${progressUpdates}`);
    console.log(`   Average rate: ${(result.deletedCount / totalTime).toFixed(2)} records/second`);
    console.log(`   Errors: ${result.errors.length}`);
    
    if (result.errors.length > 0) {
      console.log('❌ ERRORS:');
      result.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. Batch ${error.batchIndex}: ${error.error}`);
      });
    }
    
    return result;
    
  } catch (error) {
    console.error(`💥 Deletion failed for year ${year}:`, error);
    return { deletedCount: 0, errors: [{ error: error.message }] };
  }
};

/**
 * Test multi-year deletion functionality
 */
const testMultiYearDeletion = async () => {
  try {
    console.log('🧪 TESTING MULTI-YEAR BULK DELETE FUNCTIONALITY...\n');
    
    // Step 1: Create test data
    await createMultiYearTestData();
    console.log('\n');
    
    // Step 2: Count records before deletion
    console.log('📊 BEFORE DELETION:');
    const beforeCounts = {};
    for (const year of [2020, 2021, 2022, 2023]) {
      const result = await countRecordsByYear(year);
      beforeCounts[year] = result.count;
      console.log(`   ${year}: ${result.count} records`);
    }
    console.log('\n');
    
    // Step 3: Delete records for 2020, 2021, and 2022
    const deletionResults = {};
    for (const year of [2020, 2021, 2022]) {
      deletionResults[year] = await testYearDeletion(year);
      console.log(''); // Add spacing between years
    }
    
    // Step 4: Count records after deletion
    console.log('📊 AFTER DELETION:');
    const afterCounts = {};
    for (const year of [2020, 2021, 2022, 2023]) {
      const result = await countRecordsByYear(year);
      afterCounts[year] = result.count;
      console.log(`   ${year}: ${result.count} records (was ${beforeCounts[year]})`);
    }
    console.log('\n');
    
    // Step 5: Verify results
    console.log('🔍 VERIFICATION:');
    let allTestsPassed = true;
    
    // Check that 2020, 2021, 2022 records were deleted
    for (const year of [2020, 2021, 2022]) {
      if (afterCounts[year] === 0) {
        console.log(`✅ SUCCESS: All ${year} records were deleted (${deletionResults[year].deletedCount} deleted)`);
      } else {
        console.log(`❌ FAILURE: ${afterCounts[year]} ${year} records still exist`);
        allTestsPassed = false;
      }
    }
    
    // Check that 2023 records were preserved
    if (afterCounts[2023] === beforeCounts[2023]) {
      console.log(`✅ SUCCESS: 2023 records were preserved (${afterCounts[2023]} records)`);
    } else {
      console.log(`❌ FAILURE: 2023 records changed from ${beforeCounts[2023]} to ${afterCounts[2023]}`);
      allTestsPassed = false;
    }
    
    // Summary
    const totalDeleted = Object.values(deletionResults).reduce((sum, result) => sum + result.deletedCount, 0);
    const totalErrors = Object.values(deletionResults).reduce((sum, result) => sum + result.errors.length, 0);
    
    console.log('\n📈 SUMMARY:');
    console.log(`   Total records deleted: ${totalDeleted}`);
    console.log(`   Total errors: ${totalErrors}`);
    console.log(`   Overall result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
  } catch (error) {
    console.error('💥 Multi-year deletion test failed:', error);
  }
};

// Run the test
testMultiYearDeletion()
  .then(() => {
    console.log('\n🏁 Multi-year deletion test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
