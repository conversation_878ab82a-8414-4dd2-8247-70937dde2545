// Enhanced test script for progress tracking during bulk delete
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, getDocs } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

/**
 * Create a larger set of test data to better demonstrate progress tracking
 */
const createLargeTestDataset = async () => {
  try {
    console.log('🔄 Creating large test dataset for progress tracking...');
    
    const testRecords = [];
    
    // Create 50 records for 2020 (to be deleted)
    for (let i = 1; i <= 50; i++) {
      testRecords.push({
        name: `Test Customer 2020-${i.toString().padStart(3, '0')}`,
        phoneNo: `555000${i.toString().padStart(4, '0')}`,
        billNo: `TEST2020${i.toString().padStart(3, '0')}`,
        date: `${Math.floor(Math.random() * 28) + 1}-${Math.floor(Math.random() * 12) + 1}-2020`,
        deliveryDate: `${Math.floor(Math.random() * 28) + 1}-${Math.floor(Math.random() * 12) + 1}-2020`,
        imageUrl: '',
        measurements: { 
          chest: 38 + Math.floor(Math.random() * 10), 
          waist: 30 + Math.floor(Math.random() * 8) 
        },
        notes: `Test record ${i} for 2020 - should be deleted`,
        timestamp: new Date(`2020-${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 28) + 1}`).getTime()
      });
    }
    
    // Create 20 records for 2021 (should not be deleted)
    for (let i = 1; i <= 20; i++) {
      testRecords.push({
        name: `Test Customer 2021-${i.toString().padStart(3, '0')}`,
        phoneNo: `555100${i.toString().padStart(4, '0')}`,
        billNo: `TEST2021${i.toString().padStart(3, '0')}`,
        date: `${Math.floor(Math.random() * 28) + 1}-${Math.floor(Math.random() * 12) + 1}-2021`,
        deliveryDate: `${Math.floor(Math.random() * 28) + 1}-${Math.floor(Math.random() * 12) + 1}-2021`,
        imageUrl: '',
        measurements: { 
          chest: 38 + Math.floor(Math.random() * 10), 
          waist: 30 + Math.floor(Math.random() * 8) 
        },
        notes: `Test record ${i} for 2021 - should NOT be deleted`,
        timestamp: new Date(`2021-${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 28) + 1}`).getTime()
      });
    }

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    console.log(`📝 Creating ${testRecords.length} test records...`);
    let created = 0;
    
    for (const record of testRecords) {
      await addDoc(measurementsRef, record);
      created++;
      
      if (created % 10 === 0) {
        console.log(`✅ Created ${created}/${testRecords.length} records`);
      }
    }
    
    console.log(`🎉 Successfully created ${testRecords.length} test records`);
    console.log(`   - 2020 records: 50 (will be deleted)`);
    console.log(`   - 2021 records: 20 (will be preserved)`);
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  }
};

/**
 * Test the enhanced progress tracking
 */
const testProgressTracking = async () => {
  try {
    console.log('🧪 Testing enhanced progress tracking...\n');
    
    // Step 1: Create test data
    await createLargeTestDataset();
    console.log('\n');
    
    // Step 2: Import and test the delete function with detailed progress tracking
    const { deleteMeasurementsByYear } = await import('./src/services/measurementService.js');
    
    console.log('🗑️ STARTING BULK DELETE WITH PROGRESS TRACKING...\n');
    
    const progressLog = [];
    const startTime = Date.now();
    
    const result = await deleteMeasurementsByYear(2020, (progress) => {
      const currentTime = Date.now();
      const elapsedTime = (currentTime - startTime) / 1000;
      
      // Log progress with timestamps
      const logEntry = {
        timestamp: new Date(currentTime).toISOString(),
        elapsedTime: elapsedTime.toFixed(1),
        phase: progress.phase,
        progress: progress
      };
      
      progressLog.push(logEntry);
      
      // Display progress in real-time
      if (progress.phase === 'scanning') {
        const scanPercent = progress.totalCount > 0 ? ((progress.scannedCount / progress.totalCount) * 100).toFixed(1) : 0;
        console.log(`📄 [${elapsedTime.toFixed(1)}s] Scanning: ${progress.scannedCount}/${progress.totalCount} (${scanPercent}%) | Found: ${progress.foundCount}`);
      } else if (progress.phase === 'deleting') {
        const deletePercent = progress.totalToDelete > 0 ? ((progress.deletedCount / progress.totalToDelete) * 100).toFixed(1) : 0;
        const rate = progress.deletedCount / elapsedTime;
        console.log(`🗑️ [${elapsedTime.toFixed(1)}s] Deleting: ${progress.deletedCount}/${progress.totalToDelete} (${deletePercent}%) | Batch: ${progress.batchIndex}/${progress.totalBatches} | Rate: ${rate.toFixed(1)}/s`);
      } else {
        console.log(`⚡ [${elapsedTime.toFixed(1)}s] ${progress.phase}`);
      }
    });
    
    const totalTime = (Date.now() - startTime) / 1000;
    
    console.log('\n✅ BULK DELETE COMPLETED:');
    console.log(`   Total time: ${totalTime.toFixed(2)} seconds`);
    console.log(`   Deleted records: ${result.deletedCount}`);
    console.log(`   Average rate: ${(result.deletedCount / totalTime).toFixed(2)} records/second`);
    console.log(`   Errors: ${result.errors.length}`);
    
    if (result.errors.length > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:');
      result.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. Batch ${error.batchIndex}: ${error.error} (${error.documentsInBatch} docs)`);
      });
    }
    
    console.log('\n📊 PROGRESS TRACKING SUMMARY:');
    console.log(`   Total progress updates: ${progressLog.length}`);
    console.log(`   Scanning updates: ${progressLog.filter(p => p.progress.phase === 'scanning').length}`);
    console.log(`   Deletion updates: ${progressLog.filter(p => p.progress.phase === 'deleting').length}`);
    
    // Show detailed progress timeline
    console.log('\n⏱️ PROGRESS TIMELINE:');
    progressLog.forEach((entry, index) => {
      if (index < 10 || index >= progressLog.length - 5) { // Show first 10 and last 5
        console.log(`   [${entry.elapsedTime}s] ${entry.phase}: ${entry.progress.phase === 'scanning' ? `${entry.progress.scannedCount}/${entry.progress.totalCount}` : `${entry.progress.deletedCount}/${entry.progress.totalToDelete}`}`);
      } else if (index === 10) {
        console.log('   ... (middle entries omitted) ...');
      }
    });
    
  } catch (error) {
    console.error('💥 Progress tracking test failed:', error);
  }
};

// Run the test
testProgressTracking()
  .then(() => {
    console.log('\n🏁 Progress tracking test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
