// Test script for Google AI receipt scanning functionality
import { scanReceipt, getModelStats } from './src/services/receiptScanService.js';

// Mock base64 image data (small test image)
const testBase64Image = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';

async function testReceiptScan() {
  console.log('🧪 Testing receipt scanning functionality...');

  // Show available Google AI models
  const stats = getModelStats();
  console.log('📊 Available Google AI Models:');
  console.log(`   Total: ${stats.total} models`);
  console.log(`   All models support vision processing`);
  console.log('');
  console.log('📋 Google AI Model List:');
  stats.models.forEach((model, index) => {
    console.log(`   ${index + 1}. ${model.name} (${model.type})`);
  });
  console.log('');

  try {
    // Test with a mock image
    console.log('🔍 Starting receipt scan test...');
    const result = await scanReceipt(testBase64Image);

    console.log('📊 Scan Result:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ Receipt scanning test passed!');
      console.log('📋 Extracted data:', result.data);
      console.log('🤖 Model used:', result.modelUsed);
    } else {
      console.log('❌ Receipt scanning test failed:', result.error);
    }
  } catch (error) {
    console.error('💥 Test error:', error);
  }
}

// Run the test
testReceiptScan();
