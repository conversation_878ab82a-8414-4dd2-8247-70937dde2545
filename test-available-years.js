// Test script to check what years actually have data
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCJ11B1BATse018MOQ1NTKQY45zY6fg-SU",
  authDomain: "tailor-dd259.firebaseapp.com",
  projectId: "tailor-dd259",
  storageBucket: "tailor-dd259.appspot.com",
  messagingSenderId: "847981940747",
  appId: "1:847981940747:android:eb82f08a16c340f5f0b70e"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const MEASUREMENTS_COLLECTION = 'measurements';

// Test what years have data
async function testAvailableYears() {
  try {
    console.log('🧪 Testing available years in database...\n');

    const measurementsRef = collection(db, MEASUREMENTS_COLLECTION);
    
    // Get all measurements to analyze years
    console.log('📊 Fetching all measurements to analyze years...');
    const allSnapshot = await getDocs(measurementsRef);
    console.log(`📋 Total measurements in database: ${allSnapshot.size}`);

    // Analyze years
    const yearCounts = {};
    const yearData = {};

    allSnapshot.forEach((doc) => {
      const data = doc.data();
      const timestamp = data.timestamp;
      
      if (timestamp) {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        
        if (!yearCounts[year]) {
          yearCounts[year] = 0;
          yearData[year] = [];
        }
        
        yearCounts[year]++;
        yearData[year].push({
          id: doc.id,
          name: data.name || 'Unknown',
          date: data.date || '',
          timestamp: timestamp
        });
      }
    });

    // Sort years
    const availableYears = Object.keys(yearCounts).map(Number).sort((a, b) => b - a);
    
    console.log('\n📅 Years with data (newest first):');
    availableYears.forEach(year => {
      console.log(`   ${year}: ${yearCounts[year]} measurements`);
    });

    // Test loading from each year
    console.log('\n🔄 Testing year-by-year loading...');
    
    for (const year of availableYears.slice(0, 3)) { // Test first 3 years
      console.log(`\n📅 Testing year ${year}:`);
      
      // Create timestamp range for the year
      const yearStart = new Date(year, 0, 1).getTime();
      const yearEnd = new Date(year, 11, 31, 23, 59, 59).getTime();
      
      console.log(`   Timestamp range: ${yearStart} - ${yearEnd}`);
      
      try {
        // Test query for this year
        const yearQuery = query(
          measurementsRef,
          where('timestamp', '>=', yearStart),
          where('timestamp', '<=', yearEnd),
          orderBy('timestamp', 'desc'),
          limit(15)
        );
        
        const yearSnapshot = await getDocs(yearQuery);
        console.log(`   ✅ Query successful: ${yearSnapshot.size} measurements`);
        
        // Show sample data
        if (yearSnapshot.size > 0) {
          console.log(`   📝 Sample measurements:`);
          yearSnapshot.docs.slice(0, 3).forEach((doc, idx) => {
            const data = doc.data();
            const date = new Date(data.timestamp);
            console.log(`      ${idx + 1}. ${data.name || 'Unknown'} - ${date.toLocaleDateString()}`);
          });
        }
        
      } catch (error) {
        console.log(`   ❌ Query failed: ${error.message}`);
      }
    }

    // Test simple pagination without year filtering
    console.log('\n🔄 Testing simple pagination (no year filtering)...');
    
    try {
      const simpleQuery = query(
        measurementsRef,
        orderBy('timestamp', 'desc'),
        limit(20)
      );
      
      const simpleSnapshot = await getDocs(simpleQuery);
      console.log(`✅ Simple query successful: ${simpleSnapshot.size} measurements`);
      
      console.log('📝 Recent measurements:');
      simpleSnapshot.docs.slice(0, 5).forEach((doc, idx) => {
        const data = doc.data();
        const date = new Date(data.timestamp);
        console.log(`   ${idx + 1}. ${data.name || 'Unknown'} - ${date.toLocaleDateString()}`);
      });
      
    } catch (error) {
      console.log(`❌ Simple query failed: ${error.message}`);
    }

    console.log('\n🎉 Available years test completed!');
    console.log('\n📋 Summary:');
    console.log(`   Total measurements: ${allSnapshot.size}`);
    console.log(`   Years with data: ${availableYears.join(', ')}`);
    console.log(`   Most recent year: ${availableYears[0]} (${yearCounts[availableYears[0]]} measurements)`);

  } catch (error) {
    console.error('❌ Available years test failed:', error);
  }
}

// Run the test
testAvailableYears().then(() => {
  console.log('🏁 Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
